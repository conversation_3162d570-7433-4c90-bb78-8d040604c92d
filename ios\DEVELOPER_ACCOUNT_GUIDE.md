# 开发者账号配置指南

本文档说明如何在不同类型的Apple开发者账号下配置和使用SafeBaiyun应用。

## 开发者账号类型

### 1. 个人免费开发者账号 (Personal Development Team)
**当前配置**: 项目已配置为支持个人免费开发者账号

**功能限制**:
- ❌ 不支持App Groups（Widget无法与主应用共享数据）
- ❌ 不支持Associated Domains
- ❌ 应用只能在开发设备上运行7天
- ❌ 无法发布到App Store

**可用功能**:
- ✅ 主应用的所有BLE功能
- ✅ 设备扫描、连接、开门操作
- ✅ 设置界面和配置管理
- ✅ Widget界面（但无法与主应用数据同步）

### 2. 付费开发者账号 ($99/年)
**需要手动配置**: 如果你有付费开发者账号，可以启用完整功能

**完整功能**:
- ✅ App Groups支持（Widget与主应用数据同步）
- ✅ Associated Domains支持
- ✅ 可以发布到App Store
- ✅ 应用可以长期运行
- ✅ 所有企业级功能

## 配置步骤

### 个人免费账号（当前配置）
1. 在Xcode中打开项目
2. 选择你的Personal Team
3. Bundle Identifier会自动设置为：`[你的团队ID].safebaiyun`
4. Widget的Bundle Identifier会自动设置为：`[你的团队ID].safebaiyun.SafeBaiyunWidgetExtension`
5. 直接编译运行

### 付费开发者账号配置
如果你有付费开发者账号，可以按以下步骤启用完整功能：

#### 1. 恢复Entitlements文件
编辑 `ios/SafeBaiyun/SafeBaiyun.entitlements`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.application-groups</key>
    <array>
        <string>group.cn.huacheng.safebaiyun</string>
    </array>
    <key>com.apple.developer.associated-domains</key>
    <array>
        <string>applinks:safebaiyun.huacheng.cn</string>
    </array>
</dict>
</plist>
```

编辑 `ios/SafeBaiyunWidgetExtension/SafeBaiyunWidgetExtension.entitlements`:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.application-groups</key>
    <array>
        <string>group.cn.huacheng.safebaiyun</string>
    </array>
</dict>
</plist>
```

#### 2. 在Apple Developer Portal配置
1. 登录 [Apple Developer Portal](https://developer.apple.com)
2. 创建App Groups:
   - 名称: SafeBaiyun App Group
   - 标识符: group.cn.huacheng.safebaiyun
3. 创建App ID并启用App Groups功能
4. 创建Provisioning Profile

#### 3. 在Xcode中配置
1. 选择你的付费开发者团队
2. 在Capabilities中启用App Groups
3. 选择创建的App Group
4. 编译运行

## 功能对比

| 功能 | 个人免费账号 | 付费开发者账号 |
|------|-------------|---------------|
| 主应用BLE功能 | ✅ | ✅ |
| 设备扫描连接 | ✅ | ✅ |
| 开门操作 | ✅ | ✅ |
| 设置管理 | ✅ | ✅ |
| Widget界面 | ✅ | ✅ |
| Widget数据同步 | ❌ | ✅ |
| App Store发布 | ❌ | ✅ |
| 长期运行 | ❌ (7天) | ✅ |

## 推荐方案

### 开发测试阶段
使用个人免费账号即可，主要功能都能正常使用。

### 生产部署阶段
建议升级到付费开发者账号，以获得完整功能和App Store发布能力。

## 常见问题

**Q: 为什么Widget不能显示实时状态？**
A: 个人免费账号不支持App Groups，Widget无法与主应用共享数据。升级到付费账号可解决。

**Q: 应用为什么7天后无法使用？**
A: 个人免费账号的限制。升级到付费账号可获得长期使用权限。

**Q: 如何发布到App Store？**
A: 需要付费开发者账号。配置完整的entitlements后即可提交审核。

## 技术支持

如有问题，请检查：
1. 开发者账号类型是否正确
2. Bundle Identifier是否唯一
3. Entitlements配置是否匹配账号类型
4. Provisioning Profile是否包含所需权限
