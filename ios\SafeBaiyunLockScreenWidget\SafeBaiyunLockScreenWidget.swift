//
//  SafeBaiyunLockScreenWidget.swift
//  SafeBaiyunLockScreenWidget
//
//  Created by SafeBaiyun on 2024/12/27.
//

import WidgetKit
import SwiftUI
import AppIntents

// MARK: - Widget Entry
struct LockScreenWidgetEntry: TimelineEntry {
    let date: Date
    let deviceName: String
    let isConnected: Bool
    let lastOperationTime: Date?
    let batteryLevel: Int?
}

// MARK: - Timeline Provider
struct LockScreenWidgetProvider: TimelineProvider {
    func placeholder(in context: Context) -> LockScreenWidgetEntry {
        LockScreenWidgetEntry(
            date: Date(),
            deviceName: "门禁设备",
            isConnected: false,
            lastOperationTime: nil,
            batteryLevel: nil
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (LockScreenWidgetEntry) -> Void) {
        let entry = LockScreenWidgetEntry(
            date: Date(),
            deviceName: getDeviceName(),
            isConnected: getConnectionStatus(),
            lastOperationTime: getLastOperationTime(),
            batteryLevel: getBatteryLevel()
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<LockScreenWidgetEntry>) -> Void) {
        let currentDate = Date()
        let entry = LockScreenWidgetEntry(
            date: currentDate,
            deviceName: getDeviceName(),
            isConnected: getConnectionStatus(),
            lastOperationTime: getLastOperationTime(),
            batteryLevel: getBatteryLevel()
        )
        
        // 每5分钟更新一次
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        completion(timeline)
    }
    
    // MARK: - 数据获取方法
    private func getDeviceName() -> String {
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        return userDefaults.string(forKey: "widget_device_name") ?? "门禁设备"
    }

    private func getConnectionStatus() -> Bool {
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        return userDefaults.bool(forKey: "widget_is_connected")
    }

    private func getLastOperationTime() -> Date? {
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        return userDefaults.object(forKey: "widget_last_operation_time") as? Date
    }

    private func getBatteryLevel() -> Int? {
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        let level = userDefaults.integer(forKey: "widget_battery_level")
        return level > 0 ? level : nil
    }
}

// MARK: - 开门操作Intent
struct UnlockDoorIntent: AppIntent {
    static var title: LocalizedStringResource = "开门"
    static var description = IntentDescription("执行蓝牙门禁开门操作")
    
    func perform() async throws -> some IntentResult {
        // 通知主应用执行开门操作
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        userDefaults.set(Date(), forKey: "widget_unlock_request")
        userDefaults.synchronize()

        // 打开主应用 - 兼容iOS 16+
        if #available(iOS 18.0, *) {
            guard let url = URL(string: "safebaiyun://unlock") else {
                throw IntentError.appNotFound
            }
            return .result(opensIntent: OpenURLIntent(url))
        } else {
            // iOS 16-17 兼容方案：通过UserDefaults通知主应用
            userDefaults.set(true, forKey: "widget_should_open_app")
            userDefaults.synchronize()

            // 返回成功结果，主应用会通过后台刷新检测到请求
            return .result()
        }
    }
}

enum IntentError: Error, LocalizedError {
    case appNotFound
    
    var errorDescription: String? {
        switch self {
        case .appNotFound:
            return "无法打开主应用"
        }
    }
}

// MARK: - Widget配置
struct SafeBaiyunLockScreenWidget: Widget {
    let kind: String = "SafeBaiyunLockScreenWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: LockScreenWidgetProvider()) { entry in
            LockScreenWidgetView(entry: entry)
        }
        .configurationDisplayName("平安回家锁屏小组件")
        .description("在锁屏界面快速查看门禁状态并执行开门操作")
        .supportedFamilies([
            .accessoryCircular,
            .accessoryRectangular,
            .accessoryInline
        ])
    }
}

// MARK: - Widget Bundle
@main
struct SafeBaiyunWidgetBundle: WidgetBundle {
    var body: some Widget {
        SafeBaiyunLockScreenWidget()
    }
}
