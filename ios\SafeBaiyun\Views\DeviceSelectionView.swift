//
//  DeviceSelectionView.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import SwiftUI

struct DeviceSelectionView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var bleManager = BLEManager.shared
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var selectedDevice: BLEDevice?
    
    var body: some View {
        NavigationView {
            VStack {
                // 扫描状态
                if bleManager.isScanning {
                    VStack(spacing: 15) {
                        ProgressView()
                            .scaleEffect(1.2)
                        
                        Text("正在搜索设备...")
                            .font(.headline)
                        
                        Text("请确保门禁设备已开启并在附近")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
                
                // 设备列表
                if !bleManager.discoveredDevices.isEmpty {
                    List {
                        Section("发现的设备") {
                            ForEach(bleManager.discoveredDevices) { device in
                                DeviceRowView(device: device) {
                                    selectedDevice = device
                                    connectToDevice(device)
                                }
                            }
                        }
                    }
                } else if !bleManager.isScanning {
                    VStack(spacing: 20) {
                        Image(systemName: "magnifyingglass")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        
                        Text("未发现设备")
                            .font(.headline)
                            .foregroundColor(.secondary)
                        
                        Text("请确保：\n• 蓝牙已开启\n• 门禁设备在附近\n• 设备正在广播服务")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("重新扫描") {
                            bleManager.startScanning()
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                }
                
                Spacer()
                
                // 连接状态
                if bleManager.connectionState != .disconnected && bleManager.connectionState != .scanning {
                    VStack(spacing: 10) {
                        HStack {
                            Circle()
                                .fill(connectionStateColor)
                                .frame(width: 12, height: 12)
                            
                            Text(bleManager.connectionState.localizedDescription)
                                .font(.subheadline)
                                .fontWeight(.medium)
                        }
                        
                        if let device = selectedDevice {
                            Text("连接设备: \(device.displayName)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                    .padding(.horizontal)
                }
                
                // 错误信息
                if let error = bleManager.lastError {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
            }
            .navigationTitle("选择设备")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        bleManager.stopScanning()
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if bleManager.isScanning {
                        Button("停止") {
                            bleManager.stopScanning()
                        }
                    } else {
                        Button("扫描") {
                            bleManager.startScanning()
                        }
                    }
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") {
                if alertMessage.contains("绑定成功") {
                    dismiss()
                }
            }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            if !bleManager.isScanning && bleManager.discoveredDevices.isEmpty {
                bleManager.startScanning()
            }
        }
        .onDisappear {
            bleManager.stopScanning()
        }
        .onChange(of: bleManager.connectionState) { _, newState in
            handleConnectionStateChange(newState)
        }
    }
    
    // MARK: - Computed Properties
    
    private var connectionStateColor: Color {
        switch bleManager.connectionState {
        case .disconnected:
            return .gray
        case .scanning, .connecting:
            return .orange
        case .connected, .authenticating:
            return .blue
        case .ready:
            return .green
        case .error:
            return .red
        }
    }
    
    // MARK: - Methods
    
    private func connectToDevice(_ device: BLEDevice) {
        bleManager.connect(to: device)
    }
    
    private func handleConnectionStateChange(_ newState: BLEConnectionState) {
        switch newState {
        case .ready:
            // 连接成功，绑定设备
            bleManager.bindCurrentDevice()
            alertMessage = "设备连接成功！已自动绑定此设备。"
            showingAlert = true
            
        case .error:
            if let error = bleManager.lastError {
                alertMessage = "连接失败: \(error)"
                showingAlert = true
            }
            
        default:
            break
        }
    }
}

// MARK: - Device Row View
struct DeviceRowView: View {
    let device: BLEDevice
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(device.displayName)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text("UUID: \(device.peripheralUUID.uuidString.prefix(8))...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        // 信号强度指示器
                        HStack(spacing: 2) {
                            ForEach(1...5, id: \.self) { level in
                                Rectangle()
                                    .fill(level <= device.signalStrengthLevel ? .green : .gray.opacity(0.3))
                                    .frame(width: 3, height: CGFloat(level * 2 + 4))
                            }
                        }
                        
                        Text("\(device.rssi) dBm")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                // 设备详细信息
                VStack(alignment: .leading, spacing: 2) {
                    if device.hasTargetService {
                        Label("包含目标服务", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    }
                    
                    if let manufacturerData = device.manufacturerDataHex {
                        Text("制造商数据: \(manufacturerData.prefix(20))...")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Text("发现时间: \(device.discoveredTimeString)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    DeviceSelectionView()
}
