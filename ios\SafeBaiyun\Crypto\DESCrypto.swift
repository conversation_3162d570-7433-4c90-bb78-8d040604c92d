//
//  DESCrypto.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation
import CommonCrypto

/// DES加密工具类
class DESCrypto {
    
    /// DES加密数据
    /// - Parameters:
    ///   - data: 待加密的数据
    ///   - key: 加密密钥（8字节）
    /// - Returns: 加密后的数据
    static func encrypt(data: Data, key: Data) -> Data? {
        guard key.count == kCCKeySizeDES else {
            print("DES密钥长度必须为8字节")
            return nil
        }
        
        let dataLength = data.count
        let bufferSize = dataLength + kCCBlockSizeDES
        var buffer = Data(count: bufferSize)
        var numBytesEncrypted: size_t = 0
        
        let cryptStatus = buffer.withUnsafeMutableBytes { bufferBytes in
            data.withUnsafeBytes { dataBytes in
                key.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCEncrypt),
                        CCAlgorithm(kCCAlgorithmDES),
                        CCOptions(kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        kCCKeySizeDES,
                        nil, // IV
                        dataBytes.bindMemory(to: UInt8.self).baseAddress,
                        dataLength,
                        bufferBytes.bindMemory(to: UInt8.self).baseAddress,
                        bufferSize,
                        &numBytesEncrypted
                    )
                }
            }
        }
        
        guard cryptStatus == kCCSuccess else {
            print("DES加密失败，状态码: \(cryptStatus)")
            return nil
        }
        
        return buffer.prefix(numBytesEncrypted)
    }
    
    /// DES解密数据
    /// - Parameters:
    ///   - data: 待解密的数据
    ///   - key: 解密密钥（8字节）
    /// - Returns: 解密后的数据
    static func decrypt(data: Data, key: Data) -> Data? {
        guard key.count == kCCKeySizeDES else {
            print("DES密钥长度必须为8字节")
            return nil
        }
        
        let dataLength = data.count
        let bufferSize = dataLength + kCCBlockSizeDES
        var buffer = Data(count: bufferSize)
        var numBytesDecrypted: size_t = 0
        
        let cryptStatus = buffer.withUnsafeMutableBytes { bufferBytes in
            data.withUnsafeBytes { dataBytes in
                key.withUnsafeBytes { keyBytes in
                    CCCrypt(
                        CCOperation(kCCDecrypt),
                        CCAlgorithm(kCCAlgorithmDES),
                        CCOptions(kCCOptionECBMode),
                        keyBytes.bindMemory(to: UInt8.self).baseAddress,
                        kCCKeySizeDES,
                        nil, // IV
                        dataBytes.bindMemory(to: UInt8.self).baseAddress,
                        dataLength,
                        bufferBytes.bindMemory(to: UInt8.self).baseAddress,
                        bufferSize,
                        &numBytesDecrypted
                    )
                }
            }
        }
        
        guard cryptStatus == kCCSuccess else {
            print("DES解密失败，状态码: \(cryptStatus)")
            return nil
        }
        
        return buffer.prefix(numBytesDecrypted)
    }
}

// MARK: - Data Extensions
extension Data {
    /// 将Data转换为十六进制字符串
    var hexString: String {
        return map { String(format: "%02x", $0) }.joined()
    }
    
    /// 从十六进制字符串创建Data
    init?(hexString: String) {
        let cleanHexString = hexString.replacingOccurrences(of: " ", with: "")
        guard cleanHexString.count % 2 == 0 else { return nil }
        
        var data = Data()
        var index = cleanHexString.startIndex
        
        while index < cleanHexString.endIndex {
            let nextIndex = cleanHexString.index(index, offsetBy: 2)
            let byteString = String(cleanHexString[index..<nextIndex])
            
            guard let byte = UInt8(byteString, radix: 16) else { return nil }
            data.append(byte)
            
            index = nextIndex
        }
        
        self = data
    }
    
    /// 打印Data的十六进制表示（用于调试）
    func printHex(label: String = "") {
        let hexString = self.hexString
        if !label.isEmpty {
            print("\(label): \(hexString)")
        } else {
            print("Data: \(hexString)")
        }
    }
}
