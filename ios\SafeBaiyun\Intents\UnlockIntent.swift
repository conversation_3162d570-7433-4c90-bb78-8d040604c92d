//
//  UnlockIntent.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation
import AppIntents

/// 开门操作的AppIntent
struct UnlockIntent: AppIntent {
    static var title: LocalizedStringResource = "开门"
    static var description = IntentDescription("通过蓝牙连接门禁设备并执行开门操作")
    
    static var openAppWhenRun: Bool = true
    
    func perform() async throws -> some IntentResult {
        let storage = SharedStorage.shared
        
        // 检查设备是否已配置
        guard storage.isDeviceConfigured else {
            let result = OperationResult.error("设备未配置", code: "NOT_CONFIGURED")
            storage.saveOperationResult(result)
            storage.updateWidgetData()
            
            return .result(dialog: "请先在应用中配置设备信息")
        }
        
        // 检查是否已绑定设备
        guard storage.boundDeviceUUID != nil else {
            let result = OperationResult.error("设备未绑定", code: "NOT_BOUND")
            storage.saveOperationResult(result)
            storage.updateWidgetData()
            
            return .result(dialog: "请先在应用中绑定设备")
        }
        
        // 记录开门尝试
        let attemptResult = OperationResult(success: false, message: "正在尝试开门...")
        storage.saveOperationResult(attemptResult)
        storage.updateWidgetData()
        
        // 由于Widget扩展无法直接执行BLE操作，
        // 这里只是更新状态，实际的BLE操作将在主应用中执行
        
        // 发送通知给主应用
        let notificationContent = [
            "action": "unlock",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        NotificationCenter.default.post(
            name: Notification.Name("WidgetUnlockRequest"),
            object: nil,
            userInfo: notificationContent
        )
        
        return .result(dialog: "正在打开应用执行开门操作...")
    }
}

/// 重新连接设备的AppIntent
struct ReconnectIntent: AppIntent {
    static var title: LocalizedStringResource = "重新连接"
    static var description = IntentDescription("重新连接到门禁设备")
    
    static var openAppWhenRun: Bool = true
    
    func perform() async throws -> some IntentResult {
        let storage = SharedStorage.shared
        
        // 更新连接状态
        storage.saveConnectionState(.connecting)
        storage.updateWidgetData()
        
        // 发送通知给主应用
        let notificationContent = [
            "action": "reconnect",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        NotificationCenter.default.post(
            name: Notification.Name("WidgetReconnectRequest"),
            object: nil,
            userInfo: notificationContent
        )
        
        return .result(dialog: "正在重新连接设备...")
    }
}

/// 打开设置的AppIntent
struct OpenSettingsIntent: AppIntent {
    static var title: LocalizedStringResource = "打开设置"
    static var description = IntentDescription("打开应用设置界面")
    
    static var openAppWhenRun: Bool = true
    
    func perform() async throws -> some IntentResult {
        // 发送通知给主应用
        let notificationContent = [
            "action": "open_settings",
            "timestamp": Date().timeIntervalSince1970
        ] as [String: Any]
        
        NotificationCenter.default.post(
            name: Notification.Name("WidgetOpenSettingsRequest"),
            object: nil,
            userInfo: notificationContent
        )
        
        return .result(dialog: "正在打开设置...")
    }
}
