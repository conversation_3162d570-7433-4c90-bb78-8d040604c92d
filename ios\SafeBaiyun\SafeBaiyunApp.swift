//
//  SafeBaiyunApp.swift
//  SafeBaiyun
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/27.
//

import SwiftUI
import WidgetKit
import Foundation

// MARK: - Widget数据管理器（简化版）
class WidgetDataManager {
    static let shared = WidgetDataManager()

    private let userDefaults: UserDefaults

    private init() {
        // 尝试使用App Groups，如果失败则使用标准UserDefaults
        if let groupDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") {
            self.userDefaults = groupDefaults
        } else {
            self.userDefaults = UserDefaults.standard
        }
    }

    private enum Keys {
        static let deviceName = "widget_device_name"
        static let isConnected = "widget_is_connected"
        static let lastOperationTime = "widget_last_operation_time"
        static let unlockRequest = "widget_unlock_request"
    }

    func updateConnectionStatus(_ isConnected: Bool) {
        userDefaults.set(isConnected, forKey: Keys.isConnected)
        userDefaults.synchronize()
        WidgetCenter.shared.reloadAllTimelines()
    }

    func updateLastOperationTime(_ date: Date) {
        userDefaults.set(date, forKey: Keys.lastOperationTime)
        userDefaults.synchronize()
        WidgetCenter.shared.reloadAllTimelines()
    }

    func syncAllDataToWidget(deviceName: String, isConnected: Bool) {
        userDefaults.set(deviceName, forKey: Keys.deviceName)
        userDefaults.set(isConnected, forKey: Keys.isConnected)
        userDefaults.set(Date(), forKey: Keys.lastOperationTime)
        userDefaults.synchronize()
        WidgetCenter.shared.reloadAllTimelines()
    }

    func hasPendingUnlockRequest() -> Bool {
        return userDefaults.object(forKey: Keys.unlockRequest) != nil || userDefaults.bool(forKey: "widget_should_open_app")
    }

    func getAndClearUnlockRequest() -> Date? {
        let request = userDefaults.object(forKey: Keys.unlockRequest) as? Date
        let shouldOpen = userDefaults.bool(forKey: "widget_should_open_app")

        if request != nil {
            userDefaults.removeObject(forKey: Keys.unlockRequest)
        }
        if shouldOpen {
            userDefaults.removeObject(forKey: "widget_should_open_app")
        }
        userDefaults.synchronize()

        return request ?? (shouldOpen ? Date() : nil)
    }
}

@main
struct SafeBaiyunApp: App {
    @StateObject private var bleManager = BLEManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.light) // 强制使用浅色模式
                .environmentObject(bleManager)
                .onOpenURL { url in
                    handleURLScheme(url)
                }
                .onAppear {
                    // 检查是否有来自Widget的开门请求
                    checkWidgetUnlockRequest()
                }
        }
    }

    // MARK: - URL Scheme处理
    private func handleURLScheme(_ url: URL) {
        guard url.scheme == "safebaiyun" else { return }

        switch url.host {
        case "unlock":
            // 执行开门操作
            performUnlockOperation()
        default:
            break
        }
    }

    // MARK: - Widget交互处理
    private func checkWidgetUnlockRequest() {
        if WidgetDataManager.shared.hasPendingUnlockRequest() {
            // 有来自Widget的开门请求
            _ = WidgetDataManager.shared.getAndClearUnlockRequest()
            performUnlockOperation()
        }
    }

    private func performUnlockOperation() {
        Task {
            await MainActor.run {
                // 如果设备已连接，直接执行开门
                if bleManager.connectionState == .connected {
                    bleManager.performUnlockOperation()
                } else {
                    // 如果设备未连接，尝试重新连接后开门
                    bleManager.reconnectAndUnlock()
                }
            }
        }
    }
}
