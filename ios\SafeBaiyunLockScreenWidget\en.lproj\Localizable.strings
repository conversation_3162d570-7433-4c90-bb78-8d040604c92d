/* 
  Localizable.strings
  SafeBaiyunLockScreenWidget

  Created by SafeBaiyun on 2024/12/27.
  
*/

/* Widget display names */
"widget_display_name" = "SafeBaiyun Lock Screen Widget";
"widget_description" = "Quickly view door lock status and perform unlock operations on the lock screen";

/* Device status */
"device_connected" = "Connected";
"device_disconnected" = "Disconnected";
"device_offline" = "Device Offline";
"door_locked" = "Door Locked";
"door_unlocked" = "Door Unlocked";

/* Operations */
"unlock_door" = "Unlock";
"unlock_action" = "Perform Bluetooth door unlock operation";
"last_operation" = "Last Operation";
"battery_level" = "Battery Level";

/* Error messages */
"app_not_found" = "Unable to open main app";
"connection_failed" = "Connection Failed";
"operation_failed" = "Operation Failed";

/* Default device name */
"default_device_name" = "Door Lock Device";

/* Time formats */
"time_format" = "HH:mm";
"date_format" = "MM-dd";

/* Status descriptions */
"status_connected" = "Door Lock Connected";
"status_disconnected" = "Device Offline";
"status_connecting" = "Connecting";
"status_unknown" = "Status Unknown";
