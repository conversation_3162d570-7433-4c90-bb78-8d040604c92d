//
//  WidgetDataManager.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation
import WidgetKit

/// Widget和主应用之间的数据共享管理器
class WidgetDataManager {
    static let shared = WidgetDataManager()
    
    // 根据开发者账号类型选择数据共享方式
    private let userDefaults: UserDefaults
    
    private init() {
        // 尝试使用App Groups，如果失败则使用标准UserDefaults
        if let groupDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") {
            self.userDefaults = groupDefaults
        } else {
            // 个人免费开发者账号回退方案
            self.userDefaults = UserDefaults.standard
        }
    }
    
    // MARK: - 数据键定义
    private enum Keys {
        static let deviceName = "widget_device_name"
        static let isConnected = "widget_is_connected"
        static let lastOperationTime = "widget_last_operation_time"
        static let batteryLevel = "widget_battery_level"
        static let unlockRequest = "widget_unlock_request"
        static let deviceMAC = "widget_device_mac"
        static let connectionStatus = "widget_connection_status"
    }
    
    // MARK: - 设备信息管理
    
    /// 更新设备名称
    func updateDeviceName(_ name: String) {
        userDefaults.set(name, forKey: Keys.deviceName)
        userDefaults.synchronize()
        reloadWidgets()
    }
    
    /// 获取设备名称
    func getDeviceName() -> String {
        return userDefaults.string(forKey: Keys.deviceName) ?? NSLocalizedString("default_device_name", comment: "默认设备名称")
    }
    
    /// 更新连接状态
    func updateConnectionStatus(_ isConnected: Bool) {
        userDefaults.set(isConnected, forKey: Keys.isConnected)
        userDefaults.synchronize()
        reloadWidgets()
    }
    
    /// 获取连接状态
    func getConnectionStatus() -> Bool {
        return userDefaults.bool(forKey: Keys.isConnected)
    }
    
    /// 更新最后操作时间
    func updateLastOperationTime(_ date: Date) {
        userDefaults.set(date, forKey: Keys.lastOperationTime)
        userDefaults.synchronize()
        reloadWidgets()
    }
    
    /// 获取最后操作时间
    func getLastOperationTime() -> Date? {
        return userDefaults.object(forKey: Keys.lastOperationTime) as? Date
    }
    
    /// 更新电池电量
    func updateBatteryLevel(_ level: Int) {
        userDefaults.set(level, forKey: Keys.batteryLevel)
        userDefaults.synchronize()
        reloadWidgets()
    }
    
    /// 获取电池电量
    func getBatteryLevel() -> Int? {
        let level = userDefaults.integer(forKey: Keys.batteryLevel)
        return level > 0 ? level : nil
    }
    
    /// 更新设备MAC地址
    func updateDeviceMAC(_ mac: String) {
        userDefaults.set(mac, forKey: Keys.deviceMAC)
        userDefaults.synchronize()
    }
    
    /// 获取设备MAC地址
    func getDeviceMAC() -> String? {
        return userDefaults.string(forKey: Keys.deviceMAC)
    }
    
    // MARK: - Widget交互管理
    
    /// 设置开门请求
    func setUnlockRequest() {
        userDefaults.set(Date(), forKey: Keys.unlockRequest)
        userDefaults.synchronize()
    }
    
    /// 获取并清除开门请求
    func getAndClearUnlockRequest() -> Date? {
        let request = userDefaults.object(forKey: Keys.unlockRequest) as? Date
        if request != nil {
            userDefaults.removeObject(forKey: Keys.unlockRequest)
            userDefaults.synchronize()
        }
        return request
    }
    
    /// 检查是否有待处理的开门请求
    func hasPendingUnlockRequest() -> Bool {
        return userDefaults.object(forKey: Keys.unlockRequest) != nil
    }
    
    // MARK: - Widget更新管理
    
    /// 刷新所有Widget
    private func reloadWidgets() {
        WidgetCenter.shared.reloadAllTimelines()
    }
    
    /// 刷新特定Widget
    func reloadLockScreenWidget() {
        WidgetCenter.shared.reloadTimelines(ofKind: "SafeBaiyunLockScreenWidget")
    }
    
    // MARK: - 数据同步
    
    /// 同步所有数据到Widget
    func syncAllDataToWidget(deviceName: String, isConnected: Bool, batteryLevel: Int? = nil) {
        userDefaults.set(deviceName, forKey: Keys.deviceName)
        userDefaults.set(isConnected, forKey: Keys.isConnected)
        
        if let battery = batteryLevel {
            userDefaults.set(battery, forKey: Keys.batteryLevel)
        }
        
        userDefaults.set(Date(), forKey: Keys.lastOperationTime)
        userDefaults.synchronize()
        
        reloadWidgets()
    }
    
    /// 清除所有Widget数据
    func clearAllWidgetData() {
        userDefaults.removeObject(forKey: Keys.deviceName)
        userDefaults.removeObject(forKey: Keys.isConnected)
        userDefaults.removeObject(forKey: Keys.lastOperationTime)
        userDefaults.removeObject(forKey: Keys.batteryLevel)
        userDefaults.removeObject(forKey: Keys.unlockRequest)
        userDefaults.removeObject(forKey: Keys.deviceMAC)
        userDefaults.synchronize()
        
        reloadWidgets()
    }
    
    // MARK: - 调试信息
    
    /// 获取所有Widget数据（用于调试）
    func getAllWidgetData() -> [String: Any] {
        return [
            "deviceName": getDeviceName(),
            "isConnected": getConnectionStatus(),
            "lastOperationTime": getLastOperationTime() ?? "无",
            "batteryLevel": getBatteryLevel() ?? "未知",
            "deviceMAC": getDeviceMAC() ?? "未设置",
            "hasPendingUnlock": hasPendingUnlockRequest()
        ]
    }
}
