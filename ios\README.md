# 平安回家 iOS 版本

这是广州市白云区蓝牙门禁应用的iOS移植版本，支持iOS 17.0+系统。

## 功能特性

### 🔐 核心功能
- **蓝牙门禁开门**：通过BLE连接门禁设备并执行开门操作
- **设备自动绑定**：首次连接后自动绑定设备，后续可直接连接
- **Widget支持**：支持iOS桌面Widget快速开门
- **完整协议兼容**：与Android版本使用相同的加密协议

### 📱 iOS特性适配
- **设备发现机制**：由于iOS限制，采用服务UUID扫描 + 用户选择的方式
- **设备持久化**：使用CBPeripheral UUID进行设备绑定和重连
- **Widget集成**：通过AppIntents实现Widget到主应用的操作传递
- **App Groups**：主应用和Widget之间的数据共享

## 技术架构

### BLE连接策略
1. **初始绑定流程**：
   - 扫描广播目标服务UUID的设备
   - 显示设备列表（包含设备名、信号强度、制造商数据）
   - 用户选择正确设备进行连接和绑定
   - 存储设备的CBPeripheral UUID

2. **后续连接流程**：
   - 使用存储的UUID通过`retrievePeripherals(withIdentifiers:)`获取设备
   - 直接连接已绑定的设备
   - MAC地址仍用于加密算法（保持协议兼容）

### 加密算法
完整移植Android版本的加密逻辑：
- DES加密算法
- 协议包格式：`[0xA5][长度][0x05][MAC[2-5]][0x00][0x01][0x07][加密数据][校验和][0x5A]`
- 校验和计算和数据填充逻辑

### Widget实现
- **WidgetKit扩展**：显示设备状态和提供操作按钮
- **AppIntents框架**：处理Widget的用户操作
- **数据共享**：通过App Groups在主应用和Widget间共享状态

## 项目结构

```
ios/
├── SafeBaiyun/                 # 主应用
│   ├── SafeBaiyunApp.swift    # 应用入口
│   ├── Views/                 # SwiftUI界面
│   │   ├── ContentView.swift  # 主界面
│   │   ├── SettingsView.swift # 设置界面
│   │   └── DeviceSelectionView.swift # 设备选择界面
│   ├── BLE/                   # 蓝牙模块
│   │   ├── BLEManager.swift   # BLE管理器
│   │   └── BLEDevice.swift    # 设备模型
│   ├── Crypto/                # 加密模块
│   │   ├── LockCrypto.swift   # 门锁加密逻辑
│   │   └── DESCrypto.swift    # DES加密实现
│   ├── Storage/               # 存储模块
│   │   └── SharedStorage.swift # 共享存储管理
│   ├── Shared/                # 共享模块
│   │   ├── Constants.swift    # 常量定义
│   │   └── SharedModels.swift # 数据模型
│   └── Intents/               # AppIntents
│       └── UnlockIntent.swift # 开门Intent
├── SafeBaiyunWidgetExtension/  # Widget扩展
│   └── SafeBaiyunWidget.swift # Widget实现
└── Shared/                    # 共享资源
```

## 使用说明

### 首次配置
1. 打开应用，点击"配置设备"
2. 输入从Android应用数据库提取的MAC地址和加密密钥
3. 保存配置后点击"搜索并绑定设备"
4. 从发现的设备列表中选择正确的门禁设备
5. 连接成功后设备将自动绑定

### 日常使用
1. **主应用开门**：打开应用点击"开门"按钮
2. **Widget开门**：在桌面Widget上直接点击开门按钮
3. **重新连接**：如果连接断开，可点击"重新连接"

### 获取配置信息
参考Android版本的提取方法：
- 有Root权限：访问`/data/data/com.huacheng.baiyunuser/databases/`
- 无Root权限：使用手机备份功能提取应用数据
- 在数据库t_device表中：`MAC_NUM`是MAC地址，`PRODUCT_KEY`是加密密钥

## 开发环境

- **Xcode**: 15.0+
- **iOS部署目标**: 17.0+
- **Swift版本**: 5.0+
- **框架依赖**：
  - CoreBluetooth
  - WidgetKit
  - AppIntents
  - SwiftUI
  - Combine

## 权限要求

- **蓝牙权限**：`NSBluetoothAlwaysUsageDescription`
- **后台蓝牙**：`bluetooth-central` background mode
- **App Groups**：用于主应用和Widget数据共享

## 注意事项

1. **iOS BLE限制**：
   - 无法获取外围设备的MAC地址
   - 无法直接通过MAC地址连接设备
   - 需要用户手动选择设备进行首次绑定

2. **Widget限制**：
   - Widget扩展无法直接执行BLE操作
   - 所有BLE操作都在主应用中执行
   - Widget通过AppIntents触发主应用操作

3. **兼容性**：
   - 加密算法与Android版本完全兼容
   - 协议格式保持一致
   - MAC地址仍用于加密算法header

## 故障排除

### 常见问题
1. **设备搜索不到**：
   - 确认蓝牙已开启
   - 确认门禁设备在附近且正常工作
   - 检查设备是否在广播目标服务

2. **连接失败**：
   - 检查设备是否被其他应用占用
   - 尝试重启蓝牙
   - 重新扫描设备

3. **开门失败**：
   - 确认MAC地址和密钥配置正确
   - 检查设备连接状态
   - 查看操作日志了解详细错误

### 调试功能
- **操作日志**：主界面点击"日志"查看详细操作记录
- **连接状态**：实时显示BLE连接状态
- **错误提示**：详细的错误信息和解决建议

## 更新日志

### v1.0.0 (2024-12-27)
- 初始版本发布
- 完整的BLE门禁开门功能
- iOS Widget支持
- 中英文本地化
- 完整的错误处理和用户提示
