//
//  LockScreenWidgetView.swift
//  SafeBaiyunLockScreenWidget
//
//  Created by SafeBaiyun on 2024/12/27.
//

import SwiftUI
import WidgetKit

// MARK: - 主Widget视图
struct LockScreenWidgetView: View {
    let entry: LockScreenWidgetEntry
    @Environment(\.widgetFamily) var widgetFamily
    
    var body: some View {
        switch widgetFamily {
        case .accessoryCircular:
            CircularLockScreenWidget(entry: entry)
        case .accessoryRectangular:
            RectangularLockScreenWidget(entry: entry)
        case .accessoryInline:
            InlineLockScreenWidget(entry: entry)
        default:
            Text("不支持的小组件尺寸")
        }
    }
}

// MARK: - 圆形锁屏小组件
struct CircularLockScreenWidget: View {
    let entry: LockScreenWidgetEntry
    
    var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(
                    entry.isConnected ? Color.green : Color.gray,
                    lineWidth: 3
                )
            
            // 中心图标
            Image(systemName: entry.isConnected ? "lock.open.fill" : "lock.fill")
                .font(.title2)
                .foregroundColor(entry.isConnected ? .green : .gray)
        }
        .widgetURL(URL(string: "safebaiyun://unlock"))
    }
}

// MARK: - 矩形锁屏小组件
struct RectangularLockScreenWidget: View {
    let entry: LockScreenWidgetEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            // 第一行：设备名称和状态
            HStack {
                Image(systemName: entry.isConnected ? "lock.open.fill" : "lock.fill")
                    .foregroundColor(entry.isConnected ? .green : .gray)
                    .font(.caption)
                
                Text(entry.deviceName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Spacer()
                
                // 连接状态指示器
                Circle()
                    .fill(entry.isConnected ? Color.green : Color.gray)
                    .frame(width: 6, height: 6)
            }
            
            // 第二行：状态信息
            HStack {
                Text(entry.isConnected ? "已连接" : "未连接")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                // 最后操作时间或电池电量
                if let lastTime = entry.lastOperationTime {
                    Text(formatTime(lastTime))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                } else if let battery = entry.batteryLevel {
                    Text("\(battery)%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .widgetURL(URL(string: "safebaiyun://unlock"))
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// MARK: - 内联锁屏小组件
struct InlineLockScreenWidget: View {
    let entry: LockScreenWidgetEntry
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: entry.isConnected ? "lock.open.fill" : "lock.fill")
                .foregroundColor(entry.isConnected ? .green : .gray)
                .font(.caption)
            
            Text(statusText)
                .font(.caption)
                .lineLimit(1)
        }
        .widgetURL(URL(string: "safebaiyun://unlock"))
    }
    
    private var statusText: String {
        if entry.isConnected {
            return "门锁已连接"
        } else {
            return "设备离线"
        }
    }
}

// MARK: - 预览
struct LockScreenWidgetView_Previews: PreviewProvider {
    static var previews: some View {
        let connectedEntry = LockScreenWidgetEntry(
            date: Date(),
            deviceName: "门禁设备A",
            isConnected: true,
            lastOperationTime: Date(),
            batteryLevel: 85
        )
        
        let disconnectedEntry = LockScreenWidgetEntry(
            date: Date(),
            deviceName: "门禁设备A",
            isConnected: false,
            lastOperationTime: nil,
            batteryLevel: nil
        )
        
        Group {
            // 圆形小组件预览
            CircularLockScreenWidget(entry: connectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryCircular))
                .previewDisplayName("圆形 - 已连接")
            
            CircularLockScreenWidget(entry: disconnectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryCircular))
                .previewDisplayName("圆形 - 未连接")
            
            // 矩形小组件预览
            RectangularLockScreenWidget(entry: connectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryRectangular))
                .previewDisplayName("矩形 - 已连接")
            
            RectangularLockScreenWidget(entry: disconnectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryRectangular))
                .previewDisplayName("矩形 - 未连接")
            
            // 内联小组件预览
            InlineLockScreenWidget(entry: connectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryInline))
                .previewDisplayName("内联 - 已连接")
            
            InlineLockScreenWidget(entry: disconnectedEntry)
                .previewContext(WidgetPreviewContext(family: .accessoryInline))
                .previewDisplayName("内联 - 未连接")
        }
    }
}
