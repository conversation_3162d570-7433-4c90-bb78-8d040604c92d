//
//  LockCrypto.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation

/// 门锁加密业务逻辑类，完整移植Android的LockBiz
class LockCrypto {
    
    /// 加密数据，生成门锁协议包
    /// - Parameters:
    ///   - inputData: 从设备读取的随机数据
    ///   - headerData: MAC地址转换的字节数组（6字节）
    ///   - keyString: 用户配置的加密密钥（十六进制字符串）
    /// - Returns: 加密后的协议包数据
    static func encryptData(inputData: Data, headerData: Data, keyString: String) -> Data? {
        // 将密钥字符串转换为字节数组
        guard let keyBytes = hexToBytes(keyString) else {
            print("密钥格式错误")
            return nil
        }
        
        // 提取MAC地址的第2-5字节作为header（索引2-5，共4字节）
        guard headerData.count >= 6 else {
            print("MAC地址数据长度不足")
            return nil
        }
        
        let headerBytesSubset = Data(headerData[2..<6]) // 提取第2-5字节
        
        print("原始MAC地址: \(headerData.hexString)")
        print("提取的Header: \(headerBytesSubset.hexString)")
        
        // 计算校验和
        var sum = 0
        
        // 累加输入数据的每个字节
        for byte in inputData {
            sum += Int(byte)
        }
        
        // 累加密钥的每个字节
        for byte in keyBytes {
            sum += Int(byte)
        }
        
        // 将校验和转换为2字节（小端序）
        let sumBytes = Data([
            UInt8(sum & 0xFF),           // 低字节
            UInt8((sum >> 8) & 0xFF)     // 高字节
        ])
        
        // 构造待加密数据：校验和(2字节) + 输入数据 + 填充到8字节倍数
        var paddedData = Data()
        paddedData.append(sumBytes)
        paddedData.append(inputData)
        
        // 计算需要填充的长度
        let paddedLength = ((paddedData.count + 7) / 8) * 8 // 向上取整到8的倍数
        
        // 填充0到指定长度
        while paddedData.count < paddedLength {
            paddedData.append(0)
        }
        
        print("校验和: \(sum)")
        print("加密前数据: \(paddedData.hexString)")
        
        // 使用DES加密（只取前8字节）
        let dataToEncrypt = paddedData.prefix(8)
        guard let encryptedBlock = DESCrypto.encrypt(data: dataToEncrypt, key: keyBytes) else {
            print("DES加密失败")
            return nil
        }
        
        // 只取加密结果的前8字节
        let encryptedData = encryptedBlock.prefix(8)
        print("加密后数据: \(encryptedData.hexString)")
        
        // 构造最终协议包
        let finalDataLength = UInt8(encryptedData.count + 12) // 8字节加密数据 + 12字节固定部分
        var finalData = Data()
        
        // 协议包格式：
        // [0xA5] [长度] [0x05] [MAC[2-5]] [0x00] [0x01] [0x07] [加密数据8字节] [校验和] [0x5A]
        finalData.append(Constants.protocolHeader)      // 0xA5
        finalData.append(finalDataLength)               // 长度
        finalData.append(Constants.protocolCommand)     // 0x05
        finalData.append(headerBytesSubset)             // MAC地址的第2-5字节
        finalData.append(contentsOf: Constants.protocolFixedFields) // [0x00, 0x01, 0x07]
        finalData.append(encryptedData)                 // 加密数据8字节
        finalData.append(0)                             // 校验和占位符
        finalData.append(Constants.protocolTail)        // 0x5A
        
        // 计算整个包的校验和
        var checksum = 0
        for byte in finalData {
            checksum += Int(byte)
        }
        
        // 将校验和的反码写入倒数第二个字节
        let checksumIndex = finalData.count - 2
        finalData[checksumIndex] = UInt8((~checksum) & 0xFF)
        
        print("最终协议包: \(finalData.hexString)")
        return finalData
    }
    
    /// 将MAC地址字符串转换为字节数组
    /// - Parameter macString: MAC地址字符串，格式如 "AA:BB:CC:DD:EE:FF"
    /// - Returns: 6字节的数据
    static func macStringToBytes(_ macString: String) -> Data? {
        let hexPairs = macString.split(separator: ":").map(String.init)
        
        guard hexPairs.count == 6 else {
            print("MAC地址格式错误，应为 AA:BB:CC:DD:EE:FF 格式")
            return nil
        }
        
        var resultBytes = Data()
        
        for hexPair in hexPairs {
            guard let byte = UInt8(hexPair, radix: 16) else {
                print("MAC地址包含无效的十六进制字符: \(hexPair)")
                return nil
            }
            resultBytes.append(byte)
        }
        
        return resultBytes
    }
    
    /// 将十六进制字符串转换为字节数组
    /// - Parameter hexString: 十六进制字符串
    /// - Returns: 对应的字节数据
    private static func hexToBytes(_ hexString: String) -> Data? {
        // 移除可能的空格和分隔符
        let cleanHexString = hexString.replacingOccurrences(of: " ", with: "")
                                     .replacingOccurrences(of: ":", with: "")
                                     .replacingOccurrences(of: "-", with: "")
        
        guard cleanHexString.count % 2 == 0 else {
            print("十六进制字符串长度必须为偶数")
            return nil
        }
        
        var data = Data()
        var index = cleanHexString.startIndex
        
        while index < cleanHexString.endIndex {
            let nextIndex = cleanHexString.index(index, offsetBy: 2)
            let byteString = String(cleanHexString[index..<nextIndex])
            
            guard let byte = UInt8(byteString, radix: 16) else {
                print("无效的十六进制字符: \(byteString)")
                return nil
            }
            
            data.append(byte)
            index = nextIndex
        }
        
        // 确保密钥长度为8字节（DES要求）
        if data.count < 8 {
            // 如果密钥不足8字节，用0填充
            while data.count < 8 {
                data.append(0)
            }
        } else if data.count > 8 {
            // 如果密钥超过8字节，只取前8字节
            data = data.prefix(8)
        }
        
        return data
    }
    
    /// 验证MAC地址格式
    /// - Parameter macAddress: MAC地址字符串
    /// - Returns: 是否为有效格式
    static func isValidMacAddress(_ macAddress: String) -> Bool {
        let macRegex = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let macPredicate = NSPredicate(format: "SELF MATCHES %@", macRegex)
        return macPredicate.evaluate(with: macAddress)
    }
}
