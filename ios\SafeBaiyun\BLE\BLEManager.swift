//
//  BLEManager.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation
import CoreBluetooth
import Combine
import WidgetKit

/// BLE管理器，负责设备扫描、连接和通信
class BLEManager: NSObject, ObservableObject {
    static let shared = BLEManager()
    
    // MARK: - Published Properties
    @Published var connectionState: BLEConnectionState = .disconnected
    @Published var discoveredDevices: [BLEDevice] = []
    @Published var isScanning = false
    @Published var lastError: String?
    @Published var operationLogs: [String] = []
    
    // MARK: - Private Properties
    private var centralManager: CBCentralManager!
    private var connectedPeripheral: CBPeripheral?
    private var readableCharacteristic: CBCharacteristic?
    private var writeableCharacteristic: CBCharacteristic?
    private var notificationCharacteristics: [CBCharacteristic] = []
    
    private var connectionTimer: Timer?
    private var scanTimer: Timer?
    
    private let targetServiceUUID = CBUUID(string: Constants.magicServiceUUID)
    private let storage = SharedStorage.shared
    
    // MARK: - Initialization
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
        addLog("BLE管理器初始化完成")
    }
    
    // MARK: - Public Methods
    
    /// 开始扫描设备
    func startScanning() {
        guard centralManager.state == .poweredOn else {
            addLog("蓝牙未开启，无法扫描")
            lastError = "请开启蓝牙"
            return
        }
        
        guard !isScanning else {
            addLog("正在扫描中...")
            return
        }
        
        discoveredDevices.removeAll()
        isScanning = true
        connectionState = .scanning
        lastError = nil
        
        // 修改扫描策略：不指定服务UUID，扫描所有设备
        // 因为目标设备没有在广告数据中包含服务UUID
        centralManager.scanForPeripherals(withServices: nil, options: [
            CBCentralManagerScanOptionAllowDuplicatesKey: false
        ])
        
        addLog("开始扫描设备...")
        
        // 设置扫描超时
        scanTimer = Timer.scheduledTimer(withTimeInterval: Constants.scanTimeout, repeats: false) { [weak self] _ in
            self?.stopScanning()
        }
    }
    
    /// 停止扫描设备
    func stopScanning() {
        guard isScanning else { return }
        
        centralManager.stopScan()
        isScanning = false
        scanTimer?.invalidate()
        scanTimer = nil
        
        if connectionState == .scanning {
            connectionState = .disconnected
        }
        
        addLog("停止扫描，发现 \(discoveredDevices.count) 个设备")
    }
    
    /// 连接到指定设备
    func connect(to device: BLEDevice) {
        guard let peripheral = device.peripheral else {
            addLog("设备对象无效")
            lastError = "设备对象无效"
            return
        }
        
        guard centralManager.state == .poweredOn else {
            addLog("蓝牙未开启")
            lastError = "请开启蓝牙"
            return
        }
        
        stopScanning()
        
        connectedPeripheral = peripheral
        connectionState = .connecting
        lastError = nil
        
        addLog("尝试连接设备: \(device.displayName)")
        
        centralManager.connect(peripheral, options: nil)
        
        // 设置连接超时
        connectionTimer = Timer.scheduledTimer(withTimeInterval: Constants.connectionTimeout, repeats: false) { [weak self] _ in
            self?.handleConnectionTimeout()
        }
    }
    
    /// 使用存储的设备UUID连接
    func connectToStoredDevice() {
        guard let deviceUUID = storage.boundDeviceUUID else {
            addLog("未找到绑定的设备")
            lastError = "请先绑定设备"
            return
        }
        
        guard centralManager.state == .poweredOn else {
            addLog("蓝牙未开启")
            lastError = "请开启蓝牙"
            return
        }
        
        let peripherals = centralManager.retrievePeripherals(withIdentifiers: [deviceUUID])
        
        guard let peripheral = peripherals.first else {
            addLog("未找到已绑定的设备，请重新扫描")
            lastError = "设备不在范围内"
            return
        }
        
        connectedPeripheral = peripheral
        connectionState = .connecting
        lastError = nil
        
        addLog("连接到已绑定设备: \(peripheral.name ?? "未知设备")")
        
        centralManager.connect(peripheral, options: nil)
        
        // 设置连接超时
        connectionTimer = Timer.scheduledTimer(withTimeInterval: Constants.connectionTimeout, repeats: false) { [weak self] _ in
            self?.handleConnectionTimeout()
        }
    }
    
    /// 断开连接
    func disconnect() {
        connectionTimer?.invalidate()
        connectionTimer = nil
        
        if let peripheral = connectedPeripheral {
            centralManager.cancelPeripheralConnection(peripheral)
            addLog("断开连接")
        }
        
        cleanup()
    }
    
    /// 执行开门操作
    func performUnlockOperation() {
        guard connectionState == .ready else {
            addLog("设备未就绪，无法执行开门操作")
            lastError = "设备未连接"
            return
        }
        
        guard let peripheral = connectedPeripheral,
              let readCharacteristic = readableCharacteristic else {
            addLog("设备特征未准备好")
            lastError = "设备特征未准备好"
            return
        }
        
        addLog("开始读取设备随机数...")
        peripheral.readValue(for: readCharacteristic)
    }
    
    /// 绑定当前连接的设备
    func bindCurrentDevice() {
        guard let peripheral = connectedPeripheral,
              connectionState.isConnected else {
            addLog("没有连接的设备可以绑定")
            lastError = "请先连接设备"
            return
        }
        
        // 更新设备配置
        if let config = storage.loadDeviceConfiguration() {
            let updatedConfig = DeviceConfiguration(
                macAddress: config.macAddress,
                encryptionKey: config.encryptionKey,
                deviceUUID: peripheral.identifier,
                deviceName: peripheral.name
            )
            storage.saveDeviceConfiguration(updatedConfig)
            addLog("设备绑定成功: \(peripheral.name ?? "未知设备")")
        } else {
            addLog("请先配置MAC地址和密钥")
            lastError = "请先配置设备信息"
        }
    }
    
    // MARK: - Private Methods
    
    private func handleConnectionTimeout() {
        addLog("连接超时")
        lastError = "连接超时"
        connectionState = .error
        disconnect()
    }
    
    private func cleanup() {
        connectedPeripheral = nil
        readableCharacteristic = nil
        writeableCharacteristic = nil
        notificationCharacteristics.removeAll()
        connectionState = .disconnected
        
        storage.saveConnectionState(connectionState)
    }
    
    private func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logMessage = "[\(timestamp)] \(message)"
        
        DispatchQueue.main.async {
            self.operationLogs.append(logMessage)
            // 限制日志数量
            if self.operationLogs.count > 100 {
                self.operationLogs.removeFirst()
            }
        }
        
        print("BLEManager: \(message)")
    }
    
    private func updateConnectionState(_ newState: BLEConnectionState) {
        DispatchQueue.main.async {
            self.connectionState = newState
            self.storage.saveConnectionState(newState)

            // 同步状态到Widget
            self.syncStatusToWidget()
        }
    }
}

// MARK: - CBCentralManagerDelegate
extension BLEManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .unknown:
            addLog("蓝牙状态未知")
        case .resetting:
            addLog("蓝牙正在重置")
        case .unsupported:
            addLog("设备不支持蓝牙")
            lastError = "设备不支持蓝牙"
        case .unauthorized:
            addLog("蓝牙权限未授权")
            lastError = "请授权蓝牙权限"
        case .poweredOff:
            addLog("蓝牙已关闭")
            lastError = "请开启蓝牙"
            stopScanning()
            disconnect()
        case .poweredOn:
            addLog("蓝牙已开启")
            lastError = nil
        @unknown default:
            addLog("未知蓝牙状态")
        }
    }

    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        // 过滤掉信号太弱的设备
        guard RSSI.intValue > -80 else { return }

        // 基于设备名称进行初步过滤（可选）
        // 门禁设备通常有特定的命名模式
        let deviceName = peripheral.name ?? ""
        let isLikelyDoorDevice = deviceName.contains("BY") ||
                                deviceName.contains("AC") ||
                                deviceName.contains("FD") ||
                                deviceName.isEmpty // 有些设备可能没有名称

        guard isLikelyDoorDevice else {
            // 跳过明显不是门禁设备的设备
            return
        }

        let device = BLEDevice(
            peripheral: peripheral,
            rssi: RSSI.intValue,
            advertisementData: advertisementData
        )

        // 检查是否已经发现过这个设备
        if !discoveredDevices.contains(where: { $0.peripheralUUID == device.peripheralUUID }) {
            addLog("发现潜在设备: \(device.deviceSummary)")

            // 由于设备没有在广告中包含服务UUID，我们需要连接验证
            // 先添加到临时列表，连接后验证是否有目标服务
            verifyDeviceServices(device)
        }
    }

    /// 验证设备是否包含目标服务
    private func verifyDeviceServices(_ device: BLEDevice) {
        guard let peripheral = device.peripheral else { return }

        // 分析广告数据，寻找门禁设备的特征
        if let advertisementData = device.advertisementData,
           let manufacturerData = advertisementData[CBAdvertisementDataManufacturerDataKey] as? Data {
            let hexString = manufacturerData.map { String(format: "%02X", $0) }.joined()
            addLog("制造商数据: \(hexString)")

            // 检查是否包含iBeacon数据或其他门禁设备特征
            if hexString.contains("4C00") { // Apple iBeacon
                addLog("检测到iBeacon设备，可能是门禁设备")
            }
        }

        // 设置临时代理来验证服务
        peripheral.delegate = self

        // 连接设备进行服务验证
        addLog("验证设备服务: \(device.deviceSummary)")
        centralManager.connect(peripheral, options: nil)

        // 设置验证超时
        DispatchQueue.main.asyncAfter(deadline: .now() + 8.0) { [weak self] in
            if peripheral.state == .connecting || (peripheral.state == .connected && self?.connectedPeripheral != peripheral) {
                self?.centralManager.cancelPeripheralConnection(peripheral)
                self?.addLog("设备服务验证超时: \(device.deviceSummary)")
            }
        }
    }

    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        connectionTimer?.invalidate()
        connectionTimer = nil

        // 检查是否是正式连接的设备
        if let connectedPeripheral = connectedPeripheral, connectedPeripheral == peripheral {
            addLog("设备连接成功: \(peripheral.name ?? "未知设备")")
            updateConnectionState(.connected)
            peripheral.delegate = self
            peripheral.discoverServices([targetServiceUUID])
        } else {
            // 这是验证连接，发现服务后立即断开
            addLog("验证连接成功: \(peripheral.name ?? "未知设备")")
            peripheral.delegate = self
            peripheral.discoverServices([targetServiceUUID])
        }
    }

    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        connectionTimer?.invalidate()
        connectionTimer = nil

        let errorMessage = error?.localizedDescription ?? "未知错误"
        addLog("连接失败: \(errorMessage)")
        lastError = "连接失败: \(errorMessage)"
        updateConnectionState(.error)

        cleanup()
    }

    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        if let error = error {
            addLog("设备意外断开: \(error.localizedDescription)")
            lastError = "连接断开: \(error.localizedDescription)"
            updateConnectionState(.error)
        } else {
            addLog("设备已断开连接")
            updateConnectionState(.disconnected)
        }

        cleanup()
    }
}

// MARK: - CBPeripheralDelegate
extension BLEManager: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            addLog("服务发现失败: \(error.localizedDescription)")
            lastError = "服务发现失败"
            updateConnectionState(.error)
            return
        }

        guard let services = peripheral.services else {
            addLog("未发现任何服务")
            lastError = "未发现服务"
            updateConnectionState(.error)
            return
        }

        addLog("发现 \(services.count) 个服务")

        // 查找目标服务
        let targetService = services.first(where: { $0.uuid == targetServiceUUID })

        // 检查是否是验证连接
        if connectedPeripheral != peripheral {
            // 这是验证连接
            if targetService != nil {
                // 找到目标服务，添加到设备列表
                let device = BLEDevice(
                    peripheral: peripheral,
                    rssi: -50, // 使用默认值，因为验证时没有RSSI
                    advertisementData: [:]
                )

                DispatchQueue.main.async {
                    if !self.discoveredDevices.contains(where: { $0.peripheralUUID == device.peripheralUUID }) {
                        self.discoveredDevices.append(device)
                        self.discoveredDevices.sort()

                        // 发送设备发现通知
                        NotificationCenter.default.post(name: .deviceDiscovered, object: device)
                    }
                }

                addLog("验证成功，发现目标设备: \(device.deviceSummary)")
            } else {
                addLog("验证失败，设备不包含目标服务")
            }

            // 断开验证连接
            centralManager.cancelPeripheralConnection(peripheral)
            return
        }

        // 正式连接的处理
        guard let targetService = targetService else {
            addLog("未找到目标服务")
            lastError = "未找到目标服务"
            updateConnectionState(.error)
            return
        }

        addLog("找到目标服务，开始发现特征...")
        peripheral.discoverCharacteristics(nil, for: targetService)
    }

    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            addLog("特征发现失败: \(error.localizedDescription)")
            lastError = "特征发现失败"
            updateConnectionState(.error)
            return
        }

        guard let characteristics = service.characteristics else {
            addLog("未发现任何特征")
            lastError = "未发现特征"
            updateConnectionState(.error)
            return
        }

        addLog("发现 \(characteristics.count) 个特征")

        // 分析特征属性
        for characteristic in characteristics {
            let properties = characteristic.properties
            addLog("特征 \(characteristic.uuid): 属性 \(properties.rawValue)")

            // 查找可读特征 (READ)
            if properties.contains(.read) {
                readableCharacteristic = characteristic
                addLog("找到可读特征")
            }

            // 查找可写特征 (WRITE)
            if properties.contains(.write) {
                writeableCharacteristic = characteristic
                addLog("找到可写特征")
            }

            // 查找通知特征 (NOTIFY 或 INDICATE)
            if properties.contains(.notify) || properties.contains(.indicate) {
                notificationCharacteristics.append(characteristic)
                addLog("找到通知特征")
            }
        }

        // 设置通知
        setupNotifications()
    }

    private func setupNotifications() {
        guard let peripheral = connectedPeripheral else { return }

        for characteristic in notificationCharacteristics {
            peripheral.setNotifyValue(true, for: characteristic)

            // 启用通知或指示
            if characteristic.properties.contains(.notify) {
                peripheral.setNotifyValue(true, for: characteristic)
            } else if characteristic.properties.contains(.indicate) {
                peripheral.setNotifyValue(true, for: characteristic)
            }
        }

        // 检查是否准备就绪
        if readableCharacteristic != nil && writeableCharacteristic != nil {
            addLog("设备特征准备完成，可以执行操作")
            updateConnectionState(.ready)
        } else {
            addLog("设备特征不完整")
            lastError = "设备特征不完整"
            updateConnectionState(.error)
        }
    }

    func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            addLog("读取特征值失败: \(error.localizedDescription)")
            lastError = "读取数据失败"
            return
        }

        guard let data = characteristic.value else {
            addLog("特征值为空")
            lastError = "读取数据为空"
            return
        }

        addLog("读取到数据: \(data.hexString)")

        // 处理读取到的数据，执行加密和写入
        handleCharacteristicRead(data)
    }

    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            addLog("写入特征值失败: \(error.localizedDescription)")
            lastError = "写入数据失败"

            let result = OperationResult.error("密钥写入失败", code: "WRITE_ERROR")
            storage.saveOperationResult(result)
        } else {
            addLog("密钥写入成功，开门完成")

            let result = OperationResult.success
            storage.saveOperationResult(result)

            // 更新Widget的最后操作时间
            updateWidgetOperationTime()
        }

        // 操作完成后断开连接
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.disconnect()
        }
    }

    private func handleCharacteristicRead(_ inputData: Data) {
        guard let config = storage.loadDeviceConfiguration() else {
            addLog("未找到设备配置")
            lastError = "请先配置设备"
            return
        }

        guard let macBytes = LockCrypto.macStringToBytes(config.macAddress) else {
            addLog("MAC地址格式错误")
            lastError = "MAC地址格式错误"
            return
        }

        addLog("开始生成加密数据...")

        guard let encryptedData = LockCrypto.encryptData(
            inputData: inputData,
            headerData: macBytes,
            keyString: config.encryptionKey
        ) else {
            addLog("数据加密失败")
            lastError = "数据加密失败"
            return
        }

        guard let peripheral = connectedPeripheral,
              let writeCharacteristic = writeableCharacteristic else {
            addLog("写入特征未准备好")
            lastError = "设备未准备好"
            return
        }

        addLog("写入加密数据: \(encryptedData.hexString)")
        peripheral.writeValue(encryptedData, for: writeCharacteristic, type: .withResponse)
    }

    // MARK: - Widget集成支持

    /// 重连并执行开门操作（用于Widget调用）
    func reconnectAndUnlock() {
        guard let config = storage.loadDeviceConfiguration(),
              let deviceUUID = config.deviceUUID else {
            addLog("没有已绑定的设备信息")
            lastError = "请先绑定设备"
            return
        }

        addLog("Widget请求开门，尝试重连设备...")

        // 如果已经连接，直接开门
        if connectionState == .ready {
            performUnlockOperation()
            return
        }

        // 尝试重连
        let peripherals = centralManager.retrievePeripherals(withIdentifiers: [deviceUUID])
        if let peripheral = peripherals.first {
            addLog("找到已绑定设备，开始连接...")

            // 创建BLEDevice对象并连接
            let device = BLEDevice(peripheral: peripheral, rssi: -50, advertisementData: nil)
            connect(to: device)

            // 设置连接成功后自动开门的标志
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                if self.connectionState == .ready {
                    self.performUnlockOperation()
                }
            }
        } else {
            addLog("未找到已绑定的设备")
            lastError = "设备不在范围内"
        }
    }

    /// 同步设备状态到Widget
    private func syncStatusToWidget() {
        let deviceName = connectedPeripheral?.name ?? storage.loadDeviceConfiguration()?.deviceName ?? "门禁设备"
        let isConnected = connectionState.isConnected

        // 直接使用UserDefaults同步数据
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        userDefaults.set(deviceName, forKey: "widget_device_name")
        userDefaults.set(isConnected, forKey: "widget_is_connected")
        userDefaults.set(Date(), forKey: "widget_last_operation_time")
        userDefaults.synchronize()

        // 刷新Widget
        WidgetCenter.shared.reloadAllTimelines()
    }

    /// 更新Widget的最后操作时间
    private func updateWidgetOperationTime() {
        let userDefaults = UserDefaults(suiteName: "group.safebaiyun.shared") ?? UserDefaults.standard
        userDefaults.set(Date(), forKey: "widget_last_operation_time")
        userDefaults.synchronize()
        WidgetCenter.shared.reloadAllTimelines()
    }
}
