/* 
  Localizable.strings
  Safe<PERSON><PERSON>yun

  Created by SafeBaiyun on 2024/12/27.
  
*/

// MARK: - App Name
"app_name" = "Safe Baiyun";
"app_subtitle" = "Bluetooth Door Access Helper";

// MARK: - Connection States
"connection_state_disconnected" = "Disconnected";
"connection_state_scanning" = "Scanning";
"connection_state_connecting" = "Connecting";
"connection_state_connected" = "Connected";
"connection_state_authenticating" = "Authenticating";
"connection_state_ready" = "Ready";
"connection_state_error" = "Error";

// MARK: - Main Interface
"unlock_button" = "Unlock";
"reconnect_button" = "Reconnect";
"settings_button" = "Settings";
"logs_button" = "Logs";
"configure_device_button" = "Configure Device";

// MARK: - Status
"connection_status" = "Connection Status";
"bound_device" = "Bound Device";
"last_operation" = "Last Operation";
"configured_device" = "Configured Device";

// MARK: - Settings
"device_settings" = "Device Settings";
"device_configuration" = "Device Configuration";
"device_info" = "Device Information";
"mac_address" = "MAC Address";
"encryption_key" = "Encryption Key";
"mac_address_placeholder" = "e.g.: AA:BB:CC:DD:EE:FF";
"encryption_key_placeholder" = "Enter encryption key";
"save_configuration" = "Save Configuration";
"search_and_bind_device" = "Search and Bind Device";
"clear_configuration" = "Clear Configuration";
"current_configuration" = "Current Configuration";
"device_name" = "Device Name";
"configuration_time" = "Configuration Time";
"binding_status" = "Binding Status";
"bound" = "Bound";
"not_bound" = "Not Bound";
"not_configured" = "Not Configured";

// MARK: - Device Selection
"select_device" = "Select Device";
"discovered_devices" = "Discovered Devices";
"no_devices_found" = "No Devices Found";
"searching_devices" = "Searching for devices...";
"ensure_device_nearby" = "Please ensure the door access device is powered on and nearby";
"rescan" = "Rescan";
"scan" = "Scan";
"stop" = "Stop";
"cancel" = "Cancel";
"connecting_device" = "Connecting Device";
"contains_target_service" = "Contains Target Service";
"manufacturer_data" = "Manufacturer Data";
"discovery_time" = "Discovery Time";
"unknown_device" = "Unknown Device";

// MARK: - Signal Strength
"signal_excellent" = "Excellent Signal";
"signal_very_good" = "Very Good Signal";
"signal_good" = "Good Signal";
"signal_fair" = "Fair Signal";
"signal_poor" = "Poor Signal";

// MARK: - Operations
"unlock_success" = "Unlock Successful";
"unlock_failed" = "Unlock Failed";
"attempting_unlock" = "Attempting to unlock...";
"device_binding_success" = "Device Binding Successful";
"device_connection_success" = "Device connected successfully! Automatically bound this device.";
"connection_failed" = "Connection Failed";

// MARK: - Errors
"bluetooth_not_enabled" = "Please enable Bluetooth";
"bluetooth_unauthorized" = "Please authorize Bluetooth permission";
"bluetooth_unsupported" = "Device does not support Bluetooth";
"device_not_configured" = "Please configure device information first";
"device_not_bound" = "Please bind device first";
"device_not_ready" = "Device not connected";
"device_not_in_range" = "Device not in range";
"invalid_mac_address" = "Invalid MAC address format";
"invalid_encryption_key" = "Invalid encryption key";
"connection_timeout" = "Connection timeout";
"service_discovery_failed" = "Service discovery failed";
"characteristic_discovery_failed" = "Characteristic discovery failed";
"read_data_failed" = "Failed to read data";
"write_data_failed" = "Failed to write data";
"encryption_failed" = "Data encryption failed";

// MARK: - Help
"help_title" = "How to get MAC address and key?";
"help_root_method" = "1. Android device with Root permission:";
"help_root_description" = "Go to /data/data/com.huacheng.baiyunuser/databases/ directory and find the database file";
"help_no_root_method" = "2. Device without Root permission:";
"help_no_root_description" = "Use phone backup function to extract app data, then view the database file";
"help_database_method" = "3. In the t_device table of the database:";
"help_database_description" = "MAC_NUM field is the MAC address, PRODUCT_KEY field is the encryption key";

// MARK: - Alerts
"alert_title" = "Notice";
"ok" = "OK";
"done" = "Done";
"close" = "Close";
"configuration_saved" = "Configuration saved successfully! You can now search and bind devices.";
"configuration_cleared" = "Configuration cleared";

// MARK: - Widget
"widget_display_name" = "Safe Baiyun";
"widget_description" = "Quick unlock and device status";
"opening_app" = "Opening app to perform unlock operation...";
"reconnecting_device" = "Reconnecting to device...";
"opening_settings" = "Opening settings...";

// MARK: - Logs
"operation_logs" = "Operation Logs";
"ble_manager_initialized" = "BLE manager initialized";
"bluetooth_state_unknown" = "Bluetooth state unknown";
"bluetooth_resetting" = "Bluetooth resetting";
"bluetooth_powered_on" = "Bluetooth powered on";
"bluetooth_powered_off" = "Bluetooth powered off";
"start_scanning" = "Start scanning devices...";
"stop_scanning" = "Stop scanning";
"device_discovered" = "Device discovered";
"connection_successful" = "Device connection successful";
"connection_disconnected" = "Device disconnected";
"services_discovered" = "Services discovered";
"characteristics_discovered" = "Characteristics discovered";
"readable_characteristic_found" = "Readable characteristic found";
"writable_characteristic_found" = "Writable characteristic found";
"notification_characteristic_found" = "Notification characteristic found";
"device_ready" = "Device characteristics ready, can perform operations";
"reading_random_data" = "Start reading device random data...";
"data_read" = "Data read";
"generating_encrypted_data" = "Start generating encrypted data...";
"writing_encrypted_data" = "Writing encrypted data";
"key_write_successful" = "Key write successful, unlock completed";
"key_write_failed" = "Key write failed";
