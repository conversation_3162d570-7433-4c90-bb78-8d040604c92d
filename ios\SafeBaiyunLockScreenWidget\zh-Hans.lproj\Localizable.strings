/* 
  Localizable.strings
  SafeBaiyunLockScreenWidget

  Created by SafeBaiyun on 2024/12/27.
  
*/

/* Widget显示名称 */
"widget_display_name" = "平安回家锁屏小组件";
"widget_description" = "在锁屏界面快速查看门禁状态并执行开门操作";

/* 设备状态 */
"device_connected" = "已连接";
"device_disconnected" = "未连接";
"device_offline" = "设备离线";
"door_locked" = "门锁已锁定";
"door_unlocked" = "门锁已解锁";

/* 操作相关 */
"unlock_door" = "开门";
"unlock_action" = "执行蓝牙门禁开门操作";
"last_operation" = "最后操作";
"battery_level" = "电池电量";

/* 错误信息 */
"app_not_found" = "无法打开主应用";
"connection_failed" = "连接失败";
"operation_failed" = "操作失败";

/* 默认设备名称 */
"default_device_name" = "门禁设备";

/* 时间格式 */
"time_format" = "HH:mm";
"date_format" = "MM-dd";

/* 状态描述 */
"status_connected" = "门锁已连接";
"status_disconnected" = "设备离线";
"status_connecting" = "正在连接";
"status_unknown" = "状态未知";
