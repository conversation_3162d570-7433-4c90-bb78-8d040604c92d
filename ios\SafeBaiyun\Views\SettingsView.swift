//
//  SettingsView.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var macAddress = ""
    @State private var encryptionKey = ""
    @State private var showingDeviceSelection = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    private let storage = SharedStorage.shared
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("设备配置")
                            .font(.headline)
                        
                        Text("请输入门禁设备的MAC地址和加密密钥。这些信息可以从原始应用的数据库中提取。")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 5)
                }
                
                Section("设备信息") {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("MAC地址")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("例如: AA:BB:CC:DD:EE:FF", text: $macAddress)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.allCharacters)
                            .disableAutocorrection(true)
                    }
                    
                    VStack(alignment: .leading, spacing: 5) {
                        Text("加密密钥")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("请输入加密密钥", text: $encryptionKey)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .disableAutocorrection(true)
                    }
                }
                
                Section("操作") {
                    Button(action: saveConfiguration) {
                        HStack {
                            Image(systemName: "checkmark.circle")
                            Text("保存配置")
                        }
                        .foregroundColor(.blue)
                    }
                    .disabled(!isValidConfiguration)
                    
                    Button(action: { showingDeviceSelection = true }) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                            Text("搜索并绑定设备")
                        }
                        .foregroundColor(.green)
                    }
                    .disabled(!storage.isDeviceConfigured)
                    
                    if storage.isDeviceConfigured {
                        Button(action: clearConfiguration) {
                            HStack {
                                Image(systemName: "trash")
                                Text("清除配置")
                            }
                            .foregroundColor(.red)
                        }
                    }
                }
                
                if storage.isDeviceConfigured {
                    Section("当前配置") {
                        if let config = storage.loadDeviceConfiguration() {
                            VStack(alignment: .leading, spacing: 10) {
                                HStack {
                                    Text("MAC地址:")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(config.macAddress)
                                        .fontWeight(.medium)
                                }
                                
                                HStack {
                                    Text("设备名称:")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(config.deviceName ?? "未绑定")
                                        .fontWeight(.medium)
                                }
                                
                                HStack {
                                    Text("配置时间:")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(formatDate(config.configuredAt))
                                        .fontWeight(.medium)
                                }
                                
                                HStack {
                                    Text("绑定状态:")
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(config.deviceUUID != nil ? "已绑定" : "未绑定")
                                        .fontWeight(.medium)
                                        .foregroundColor(config.deviceUUID != nil ? .green : .orange)
                                }
                            }
                        }
                    }
                }
                
                Section("帮助信息") {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("如何获取MAC地址和密钥？")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        Text("1. 有Root权限的Android设备：")
                            .font(.caption)
                            .fontWeight(.medium)
                        Text("前往 /data/data/com.huacheng.baiyunuser/databases/ 目录，找到数据库文件")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("2. 无Root权限的设备：")
                            .font(.caption)
                            .fontWeight(.medium)
                        Text("使用手机备份功能提取应用数据，然后查看数据库文件")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("3. 在数据库的t_device表中：")
                            .font(.caption)
                            .fontWeight(.medium)
                        Text("MAC_NUM 字段是MAC地址，PRODUCT_KEY 字段是加密密钥")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 5)
                }
            }
            .navigationTitle("设备设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingDeviceSelection) {
            DeviceSelectionView()
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        .onAppear {
            loadCurrentConfiguration()
        }
    }
    
    // MARK: - Computed Properties
    
    private var isValidConfiguration: Bool {
        return LockCrypto.isValidMacAddress(macAddress) && !encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // MARK: - Methods
    
    private func loadCurrentConfiguration() {
        if let config = storage.loadDeviceConfiguration() {
            macAddress = config.macAddress
            encryptionKey = config.encryptionKey
        }
    }
    
    private func saveConfiguration() {
        let config = DeviceConfiguration(
            macAddress: macAddress.uppercased(),
            encryptionKey: encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        storage.saveDeviceConfiguration(config)
        
        alertMessage = "配置保存成功！现在可以搜索并绑定设备。"
        showingAlert = true
    }
    
    private func clearConfiguration() {
        storage.clearDeviceConfiguration()
        macAddress = ""
        encryptionKey = ""
        
        alertMessage = "配置已清除"
        showingAlert = true
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    SettingsView()
}
