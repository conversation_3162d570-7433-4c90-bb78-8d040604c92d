//
//  ContentView.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var bleManager = BLEManager.shared
    @State private var showingSettings = false
    @State private var showingDeviceSelection = false
    @State private var showingLogs = false
    
    private let storage: SharedStorage = SharedStorage.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 标题
                VStack(spacing: 10) {
                    Image(systemName: "lock.open.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("平安回家")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("蓝牙门禁开门助手")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 50)
                
                Spacer()
                
                // 状态显示
                VStack(spacing: 15) {
                    StatusCardView(
                        title: "连接状态",
                        value: bleManager.connectionState.localizedDescription,
                        color: statusColor
                    )
                    
                    if storage.isDeviceConfigured {
                        StatusCardView(
                            title: "绑定设备",
                            value: storage.deviceName ?? "已配置设备",
                            color: .green
                        )
                    }
                    
                    if let lastResult = storage.loadOperationResult() {
                        StatusCardView(
                            title: "上次操作",
                            value: lastResult.message,
                            color: lastResult.success ? .green : .red
                        )
                    }
                }
                
                Spacer()
                
                // 主要操作按钮
                VStack(spacing: 20) {
                    if storage.isDeviceConfigured {
                        // 开门按钮
                        Button(action: performUnlock) {
                            HStack {
                                Image(systemName: "lock.open")
                                    .font(.title2)
                                Text("开门")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(unlockButtonColor)
                            .foregroundColor(.white)
                            .cornerRadius(15)
                        }
                        .disabled(!canUnlock)
                        
                        // 重新连接按钮
                        Button(action: reconnectDevice) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                Text("重新连接")
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 50)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(10)
                        }
                        .disabled(bleManager.connectionState == .connecting || bleManager.connectionState == .scanning)
                    } else {
                        // 配置设备按钮
                        Button(action: { showingSettings = true }) {
                            HStack {
                                Image(systemName: "gear")
                                Text("配置设备")
                            }
                            .frame(maxWidth: .infinity)
                            .frame(height: 60)
                            .background(Color.orange)
                            .foregroundColor(.white)
                            .cornerRadius(15)
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
                
                // 错误信息
                if let error = bleManager.lastError {
                    Text(error)
                        .foregroundColor(.red)
                        .font(.caption)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
            }
            .navigationTitle("")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("日志") {
                        showingLogs = true
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("设置") {
                        showingSettings = true
                    }
                }
            }
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingDeviceSelection) {
            DeviceSelectionView()
        }
        .sheet(isPresented: $showingLogs) {
            LogsView(logs: bleManager.operationLogs)
        }
        .onAppear {
            // 应用启动时尝试连接已绑定的设备
            if storage.isDeviceConfigured && storage.boundDeviceUUID != nil {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    bleManager.connectToStoredDevice()
                }
            }


        }
    }
    
    // MARK: - Computed Properties
    
    private var statusColor: Color {
        switch bleManager.connectionState {
        case .disconnected:
            return .gray
        case .scanning, .connecting:
            return .orange
        case .connected, .authenticating:
            return .blue
        case .ready:
            return .green
        case .error:
            return .red
        }
    }
    
    private var unlockButtonColor: Color {
        canUnlock ? .green : .gray
    }
    
    private var canUnlock: Bool {
        bleManager.connectionState == .ready
    }
    
    // MARK: - Actions
    
    private func performUnlock() {
        if bleManager.connectionState == .ready {
            bleManager.performUnlockOperation()
        } else if storage.boundDeviceUUID != nil {
            bleManager.connectToStoredDevice()
        } else {
            showingDeviceSelection = true
        }
    }
    
    private func reconnectDevice() {
        if storage.boundDeviceUUID != nil {
            bleManager.connectToStoredDevice()
        } else {
            showingDeviceSelection = true
        }
    }


}

// MARK: - Status Card View
struct StatusCardView: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 5) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.body)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
        .padding(.horizontal, 20)
    }
}

// MARK: - Logs View
struct LogsView: View {
    let logs: [String]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 5) {
                    ForEach(logs.reversed(), id: \.self) { log in
                        Text(log)
                            .font(.system(.caption, design: .monospaced))
                            .padding(.horizontal)
                    }
                }
            }
            .navigationTitle("操作日志")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ContentView()
}
