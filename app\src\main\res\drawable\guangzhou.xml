<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1080dp"
    android:height="1080dp"
    android:viewportWidth="1080"
    android:viewportHeight="1080">
  <path
      android:pathData="M546.6,46l-2.7,83.3l4.3,0z"
      android:strokeWidth="1"
      android:fillColor="#FFCD7B"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M546,126L546,126A3,3 0,0 1,549 129L549,141A3,3 0,0 1,546 144L546,144A3,3 0,0 1,543 141L543,129A3,3 0,0 1,546 126z"
      android:strokeWidth="1"
      android:fillColor="#00DDFF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M546,140L546,140A8,8 0,0 1,554 148L554,174A8,8 0,0 1,546 182L546,182A8,8 0,0 1,538 174L538,148A8,8 0,0 1,546 140z"
      android:strokeWidth="1"
      android:fillColor="#F5A623"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M545.5,171L545.5,171A13.5,13.5 0,0 1,559 184.5L559,221.5A13.5,13.5 0,0 1,545.5 235L545.5,235A13.5,13.5 0,0 1,532 221.5L532,184.5A13.5,13.5 0,0 1,545.5 171z"
      android:strokeWidth="1"
      android:fillColor="#D61B32"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M234,800m-56,0a56,56 0,1 1,112 0a56,56 0,1 1,-112 0"
      android:strokeWidth="1"
      android:fillColor="#336D2C"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M234,829L234,829C236.2,829 238,830.8 238,833L238,930L230,930L230,833C230,830.8 231.8,829 234,829Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M214.5,768.5m-65.5,0a65.5,65.5 0,1 1,131 0a65.5,65.5 0,1 1,-131 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="214.5"
          android:startY="703"
          android:endX="214.5"
          android:endY="834"
          android:type="linear">
        <item android:offset="0" android:color="#FF68B75C"/>
        <item android:offset="1" android:color="#FF37802F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M216,756L216,756C218.2,756 220,757.8 220,760L220,930L212,930L212,760C212,757.8 213.8,756 216,756Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M216.8,795.5L248.9,772.1C250.3,771.2 252.1,771.5 253.1,772.8L253.7,773.6C254.7,774.9 254.4,776.8 253,777.8L220.9,801.2C219.6,802.1 217.7,801.8 216.7,800.5L216.1,799.7C215.1,798.3 215.4,796.5 216.8,795.5Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M906,800m-56,0a56,56 0,1 1,112 0a56,56 0,1 1,-112 0"
      android:strokeWidth="1"
      android:fillColor="#336D2C"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M906,829L906,829C908.2,829 910,830.8 910,833L910,930L902,930L902,833C902,830.8 903.8,829 906,829Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M886.5,768.5m-65.5,0a65.5,65.5 0,1 1,131 0a65.5,65.5 0,1 1,-131 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="886.5"
          android:startY="703"
          android:endX="886.5"
          android:endY="834"
          android:type="linear">
        <item android:offset="0" android:color="#FF68B75C"/>
        <item android:offset="1" android:color="#FF37802F"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M888,756L888,756C890.2,756 892,757.8 892,760L892,930L884,930L884,760C884,757.8 885.8,756 888,756Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M888.8,795.5L920.9,772.1C922.3,771.2 924.1,771.5 925.1,772.8L925.7,773.6C926.7,774.9 926.4,776.8 925,777.8L892.9,801.2C891.6,802.1 889.7,801.8 888.7,800.5L888.1,799.7C887.1,798.3 887.4,796.5 888.8,795.5Z"
      android:strokeWidth="1"
      android:fillColor="#6E5B21"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M360.1,266L435.5,266C454,339.6 465.2,435.8 468.9,554.5C472.6,673.2 468.5,798.2 456.4,929.5L344,929.5C329.6,829.2 323.9,720.9 326.7,604.5C329.5,488.1 340.6,375.2 360.1,266Z"
      android:strokeWidth="1"
      android:fillColor="#C0E5E9"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M360.1,266L435.5,266C454,339.6 465.2,435.8 468.9,554.5C472.6,673.2 468.5,798.2 456.4,929.5L344,929.5C329.6,829.2 323.9,720.9 326.7,604.5C329.5,488.1 340.6,375.2 360.1,266Z"/>
    <path
        android:pathData="M382.6,290.2L390.8,307.9C390.9,308.1 390.9,308.4 390.8,308.7L385.2,320.4C385,320.9 384.4,321.1 383.9,320.9C383.7,320.8 383.5,320.6 383.4,320.4L374,302.1C373.8,301.8 373.8,301.4 374,301.1C375.2,299.1 376.4,297.2 377.6,295.2C378.7,293.5 379.8,291.8 380.8,290.1C381.1,289.6 381.7,289.5 382.2,289.8C382.4,289.9 382.5,290 382.6,290.2ZM367.7,257.6L376,275.2C376.1,275.5 376.1,275.8 375.9,276.1L370.3,287.8C370.1,288.3 369.5,288.5 369,288.3C368.8,288.2 368.6,288 368.5,287.8L359.1,269.5C359,269.1 359,268.8 359.1,268.5C360.4,266.5 361.6,264.6 362.8,262.6C363.8,260.9 364.9,259.2 366,257.5C366.3,257 366.9,256.9 367.3,257.2C367.5,257.3 367.6,257.4 367.7,257.6ZM354.7,282.6L363,300.2C363.1,300.5 363.1,300.8 362.9,301.1L357.3,312.8C357.1,313.3 356.5,313.5 356,313.3C355.8,313.2 355.6,313 355.5,312.8L346.1,294.5C346,294.1 346,293.8 346.1,293.5C347.4,291.5 348.6,289.6 349.8,287.6C350.8,285.9 351.9,284.2 353,282.5C353.3,282 353.9,281.9 354.3,282.2C354.5,282.3 354.6,282.4 354.7,282.6ZM369.1,314.7L377.2,331.9C377.3,332.1 377.3,332.4 377.2,332.7L371.7,344.8C371.5,345.3 370.9,345.5 370.4,345.3C370.2,345.2 370.1,345 370,344.8L361.1,328.4C360.9,328.1 360.9,327.8 361.1,327.5L367.3,314.7C367.6,314.2 368.2,314 368.7,314.2C368.9,314.3 369,314.5 369.1,314.7ZM385.1,345.5L390.8,357.5C391,357.8 390.9,358.1 390.7,358.4L383.4,369.9C383.1,370.4 382.5,370.5 382,370.2C381.8,370.1 381.7,369.9 381.6,369.8L376.6,358.4C376.5,358.1 376.5,357.8 376.7,357.5L383.3,345.4C383.6,344.9 384.2,344.7 384.7,345C384.9,345.1 385,345.3 385.1,345.5ZM398.5,370.7L402.9,381.9C403,382.2 403,382.5 402.8,382.8L395.9,394.1C395.6,394.5 395,394.7 394.5,394.4C394.3,394.3 394.2,394.1 394.1,393.9L389.5,383.2C389.4,382.9 389.4,382.6 389.6,382.3L396.7,370.5C397,370.1 397.6,369.9 398,370.2C398.2,370.3 398.4,370.5 398.5,370.7ZM411.1,350.1L416.4,359.1C416.6,359.4 416.6,359.8 416.4,360.1L410.6,370.9C410.3,371.3 409.7,371.5 409.2,371.3C409.1,371.2 408.9,371 408.8,370.9L403.1,360.1C402.9,359.8 403,359.4 403.2,359.1L409.4,350C409.7,349.6 410.3,349.5 410.8,349.8C410.9,349.9 411,350 411.1,350.1ZM425.1,326.5L431,335.3C431.2,335.6 431.2,336 431.1,336.3L424.6,348.9C424.4,349.4 423.8,349.6 423.3,349.3C423.1,349.2 423,349.1 422.9,348.9L416.9,338.1C416.8,337.8 416.8,337.4 416.9,337.1L423.4,326.5C423.7,326 424.4,325.9 424.8,326.2C424.9,326.2 425.1,326.3 425.1,326.5ZM439.6,345.9L449.7,362.9C449.9,363.3 449.9,363.7 449.6,364L440,377.8C439.7,378.3 439.1,378.4 438.6,378.1C438.5,378 438.4,377.9 438.3,377.7L429.1,360.8C428.9,360.5 428.9,360.1 429.1,359.8L437.9,345.9C438.2,345.4 438.8,345.3 439.3,345.6C439.4,345.6 439.5,345.8 439.6,345.9ZM423.7,371.9L432.5,387.5C432.7,387.9 432.7,388.2 432.5,388.6L425.7,399.6C425.4,400 424.8,400.2 424.3,399.9C424.2,399.8 424,399.7 423.9,399.5L415.8,383C415.6,382.7 415.6,382.4 415.8,382.1L421.9,371.9C422.2,371.4 422.8,371.3 423.3,371.6C423.5,371.7 423.6,371.8 423.7,371.9ZM409.1,394.6L417.9,410.1C418,410.4 418,410.8 417.9,411.1L411.8,421.7C411.5,422.2 410.9,422.4 410.4,422.1C410.2,422 410.1,421.9 410,421.8L401.2,406.8C401.1,406.5 401,406.1 401.2,405.8L407.3,394.6C407.6,394.1 408.2,393.9 408.7,394.2C408.8,394.3 409,394.4 409.1,394.6ZM395.9,417.5L403.4,432.1C403.6,432.4 403.6,432.8 403.4,433.1L393.7,447.7C393.4,448.2 392.8,448.3 392.3,448C392.2,447.9 392.1,447.8 392,447.6L384.6,434C384.4,433.7 384.4,433.3 384.6,433L394.1,417.5C394.4,417 395,416.8 395.5,417.1C395.7,417.2 395.8,417.4 395.9,417.5ZM379.4,445.5L386.5,459.1C386.7,459.5 386.7,459.9 386.5,460.2L379.5,470.7C379.2,471.2 378.6,471.3 378.2,471C378,470.9 377.9,470.8 377.8,470.6L370.7,457.3C370.6,457 370.6,456.6 370.8,456.3L377.7,445.4C378,445 378.6,444.8 379.1,445.1C379.2,445.2 379.3,445.3 379.4,445.5ZM364.9,468.9L371.6,482.6C371.7,482.9 371.7,483.2 371.5,483.5L362.9,497.4C362.6,497.9 362,498 361.5,497.7C361.3,497.6 361.2,497.5 361.1,497.3L355.7,485.4C355.6,485.1 355.6,484.8 355.7,484.5L363.1,468.9C363.4,468.4 364,468.2 364.5,468.4C364.7,468.5 364.8,468.7 364.9,468.9ZM349.3,496.7L355.3,509.6C355.5,510 355.5,510.3 355.3,510.6L347.3,523.6C347,524.1 346.4,524.2 345.9,523.9C345.7,523.8 345.6,523.7 345.5,523.5L340.6,513.4C340.5,513.1 340.5,512.8 340.6,512.5L347.5,496.7C347.7,496.2 348.3,496 348.8,496.2C349,496.3 349.2,496.5 349.3,496.7ZM362.3,522.5L367.7,533.7C367.9,534 367.8,534.4 367.7,534.7L359.6,548.6C359.3,549.1 358.7,549.2 358.3,549C358.1,548.9 358,548.7 357.9,548.6L351.4,537.8C351.2,537.4 351.2,537 351.4,536.7L360.6,522.4C360.9,521.9 361.5,521.8 362,522.1C362.1,522.2 362.2,522.3 362.3,522.5ZM379.1,493.7L385.2,507.8C385.4,508.1 385.3,508.5 385.1,508.8L375.2,523.5C374.9,523.9 374.3,524 373.8,523.7C373.7,523.6 373.6,523.5 373.5,523.3L367.4,510.6C367.3,510.3 367.3,510 367.5,509.7L377.3,493.6C377.6,493.1 378.2,493 378.7,493.3C378.8,493.4 379,493.5 379.1,493.7ZM393.8,470.2L401.1,483C401.3,483.4 401.2,483.8 401,484.1L392.9,496.8C392.6,497.3 392,497.4 391.6,497.1C391.4,497 391.3,496.9 391.2,496.7L384.2,482.7C384,482.4 384.1,482 384.3,481.7L392.1,470.2C392.4,469.7 393,469.6 393.5,469.9C393.6,470 393.7,470.1 393.8,470.2ZM410.7,444.5L418.3,458.4C418.5,458.7 418.5,459.1 418.3,459.4L409.3,473.4C409,473.8 408.4,474 408,473.7C407.8,473.6 407.7,473.5 407.7,473.4L399,460.2C398.8,459.9 398.8,459.5 399,459.1L409,444.5C409.3,444 409.9,443.9 410.4,444.2C410.5,444.3 410.6,444.4 410.7,444.5ZM424.8,421.6L432.3,436.6C432.5,436.9 432.4,437.3 432.3,437.6L426.4,446.9C426.2,447.4 425.5,447.5 425.1,447.2C424.9,447.2 424.8,447 424.7,446.9L416.2,432.9C416,432.6 416,432.2 416.2,431.9L423.1,421.5C423.4,421.1 424,420.9 424.5,421.3C424.6,421.3 424.7,421.5 424.8,421.6ZM440.8,398.3L449.1,411.9C449.3,412.2 449.3,412.7 449.1,413L440.5,426.4C440.2,426.9 439.6,427 439.1,426.7C439,426.6 438.9,426.5 438.8,426.4L430.9,411.7C430.7,411.4 430.7,411 430.9,410.7L439.1,398.3C439.4,397.9 440,397.7 440.5,398C440.6,398.1 440.7,398.2 440.8,398.3ZM457,421.6L465.9,437.9C466.1,438.2 466.1,438.6 465.9,438.9L457,454.4C456.7,454.8 456.1,455 455.6,454.7C455.5,454.7 455.4,454.5 455.3,454.4L444.9,438.9C444.7,438.6 444.7,438.2 444.9,437.8L455.3,421.6C455.6,421.1 456.2,421 456.7,421.3C456.8,421.4 456.9,421.5 457,421.6ZM438.8,448.5L448,464.4C448.2,464.7 448.2,465.1 448,465.4L443.6,474.2C443.4,474.7 442.8,474.9 442.3,474.6C442.1,474.6 442,474.5 441.9,474.3L430.8,458.6C430.5,458.3 430.5,457.8 430.8,457.5L437.1,448.4C437.4,447.9 438,447.8 438.5,448.1C438.6,448.2 438.7,448.3 438.8,448.5ZM425.3,468.5L435.3,484.1C435.5,484.4 435.5,484.8 435.3,485.1L425.3,500.7C425,501.1 424.4,501.3 423.9,501C423.8,500.9 423.7,500.8 423.6,500.6L414.4,485.4C414.2,485.1 414.2,484.7 414.4,484.4L423.6,468.5C423.8,468.1 424.5,467.9 424.9,468.2C425.1,468.3 425.2,468.4 425.3,468.5ZM443,494.3L453.7,510C454,510.3 454,510.8 453.7,511.1L442.9,525.8C442.6,526.3 442,526.4 441.5,526.1C441.5,526 441.4,525.9 441.3,525.8L430.4,511.1C430.1,510.8 430.1,510.3 430.4,509.9L441.3,494.3C441.6,493.8 442.3,493.7 442.7,494C442.8,494.1 442.9,494.2 443,494.3ZM461.8,519.4L476,540.4C476.3,540.8 476.3,541.2 476,541.6L464.9,555.9C464.6,556.3 464,556.4 463.5,556.1C463.5,556 463.4,556 463.3,555.9L449.2,536.3C448.9,535.9 448.9,535.5 449.2,535.1L460.1,519.4C460.4,519 461.1,518.9 461.5,519.2C461.6,519.3 461.7,519.3 461.8,519.4ZM441.8,544.3L457.2,563.8C457.5,564.2 457.5,564.7 457.2,565.1L445.7,580.5C445.3,580.9 444.7,581 444.3,580.7C444.2,580.6 444.1,580.5 444,580.5L430.1,560.9C429.9,560.5 429.9,560.1 430.1,559.7L440.2,544.4C440.5,543.9 441.1,543.8 441.6,544.1C441.7,544.2 441.7,544.2 441.8,544.3ZM424.8,570.5L437.5,589.6C437.7,590 437.7,590.5 437.4,590.8L424.9,607C424.5,607.4 423.9,607.5 423.5,607.1C423.4,607.1 423.3,607 423.2,606.9L411.4,589C411.1,588.7 411.1,588.2 411.4,587.9L423.2,570.5C423.5,570.1 424.1,569.9 424.6,570.3C424.7,570.3 424.8,570.4 424.8,570.5ZM444.8,599.4L456.7,612.7C457.1,613.1 457.1,613.6 456.8,614L443.2,632C442.9,632.4 442.3,632.5 441.8,632.2C441.7,632.1 441.7,632.1 441.6,632L430.6,617.9C430.4,617.5 430.4,617 430.6,616.6L443.3,599.5C443.6,599 444.2,598.9 444.7,599.3C444.7,599.3 444.8,599.3 444.8,599.4ZM465.4,621.3L475.6,631.5C475.9,631.8 476,632.3 475.7,632.7L462.2,654.8C461.9,655.3 461.3,655.4 460.8,655.1C460.7,655.1 460.6,655 460.6,654.9L449.9,642.7C449.6,642.4 449.5,641.8 449.8,641.5L463.9,621.4C464.2,620.9 464.8,620.8 465.3,621.1C465.3,621.2 465.4,621.2 465.4,621.3ZM466.4,572.3L477.5,584C477.8,584.4 477.9,584.9 477.6,585.3L464.8,604.8C464.5,605.3 463.8,605.4 463.4,605.1C463.3,605 463.2,604.9 463.1,604.9L452.2,590.8C451.9,590.4 451.9,589.9 452.2,589.6L464.9,572.4C465.2,571.9 465.8,571.8 466.3,572.2C466.3,572.2 466.4,572.2 466.4,572.3ZM404.6,598.4L416.8,616C417.1,616.4 417.1,616.9 416.8,617.2L404.7,632.9C404.4,633.4 403.8,633.5 403.3,633.1C403.2,633.1 403.1,633 403.1,632.8L392.2,614.5C392,614.2 392,613.7 392.2,613.4L403,598.4C403.3,597.9 403.9,597.8 404.4,598.1C404.5,598.2 404.6,598.3 404.6,598.4ZM424.6,627.2L435.8,640.9C436.1,641.3 436.1,641.8 435.8,642.2L421.7,660.4C421.3,660.9 420.7,661 420.3,660.6C420.2,660.5 420.1,660.5 420,660.4L410.1,644.5C409.9,644.2 409.9,643.7 410.2,643.4L423,627.2C423.4,626.8 424,626.7 424.4,627.1C424.5,627.1 424.5,627.2 424.6,627.2ZM444.6,651.4L455.9,665.1C456.1,665.4 456.2,665.9 455.9,666.3L439.6,689.1C439.3,689.5 438.7,689.6 438.2,689.3C438.1,689.3 438,689.2 438,689.1L426.9,673C426.7,672.6 426.7,672.1 427,671.8L443.1,651.4C443.4,651 444,650.9 444.5,651.2C444.5,651.3 444.6,651.3 444.6,651.4ZM463.9,673.8L475.8,683.3C476.3,683.6 476.3,684.3 476,684.7L455.9,711.9C455.5,712.3 454.9,712.4 454.5,712.1C454.4,712 454.3,712 454.3,711.9L445.2,700.1C445,699.8 445,699.3 445.2,698.9L462.5,674C462.8,673.5 463.4,673.4 463.9,673.7C463.9,673.7 463.9,673.7 463.9,673.8ZM482.8,690.4L493.7,699.2C494.1,699.5 494.2,700.1 493.9,700.5L472.8,733.2C472.5,733.7 471.9,733.8 471.4,733.5C471.3,733.4 471.2,733.4 471.2,733.3L462.2,721.5C461.9,721.2 461.9,720.7 462.1,720.4L481.4,690.7C481.7,690.2 482.3,690.1 482.7,690.4C482.8,690.4 482.8,690.4 482.8,690.4ZM386.6,624.7L397.2,643.4C397.4,643.8 397.4,644.2 397.1,644.5L385,660.1C384.7,660.6 384,660.6 383.6,660.3C383.5,660.2 383.4,660.1 383.4,660L373.4,642.8C373.2,642.5 373.3,642.1 373.5,641.8L384.9,624.6C385.2,624.2 385.8,624 386.3,624.3C386.4,624.4 386.5,624.5 386.6,624.7ZM403.3,653.4L413.7,670.6C414,671 413.9,671.4 413.7,671.7L401.6,688.9C401.3,689.3 400.7,689.5 400.2,689.1C400.1,689.1 400,688.9 399.9,688.8L390.1,671.7C389.9,671.3 390,670.9 390.2,670.6L401.6,653.4C401.9,652.9 402.5,652.8 403,653.1C403.1,653.2 403.2,653.3 403.3,653.4ZM421.3,681.4L431.8,698.7C432,699 431.9,699.4 431.7,699.8L420.7,715.8C420.4,716.3 419.8,716.4 419.3,716.1C419.3,716 419.2,716 419.1,715.9L407.2,699.8C406.9,699.5 406.9,699 407.2,698.6L419.6,681.4C419.9,680.9 420.6,680.8 421,681.1C421.1,681.2 421.2,681.3 421.3,681.4ZM440.3,708.4L448.5,720.1C448.7,720.5 448.7,721 448.5,721.3L436.1,738.2C435.8,738.6 435.2,738.7 434.7,738.4C434.6,738.3 434.6,738.3 434.5,738.2L426.2,726.9C425.9,726.5 425.9,726.1 426.2,725.7L438.7,708.3C439,707.9 439.6,707.8 440.1,708.1C440.2,708.2 440.3,708.3 440.3,708.4ZM456.4,730.2L465.3,740.9C465.6,741.2 465.6,741.7 465.4,742.1L454.4,759.7C454.1,760.2 453.5,760.3 453.1,760C453,760 452.9,759.9 452.9,759.9L442.3,749C442,748.6 442,748.1 442.3,747.7L454.8,730.3C455.1,729.9 455.7,729.8 456.2,730.1C456.2,730.1 456.3,730.2 456.4,730.2ZM473.6,751.6L484,761.3C484.4,761.7 484.5,762.3 484.1,762.7L471.3,777.9C471,778.3 470.3,778.4 469.9,778C469.9,778 469.9,778 469.9,777.9L461.3,769.5C460.9,769.2 460.9,768.6 461.2,768.2L472.1,751.8C472.4,751.4 473,751.2 473.5,751.5C473.5,751.6 473.5,751.6 473.6,751.6ZM368.3,651.8L377,670.4C377.1,670.7 377.1,671.1 376.9,671.4L367.9,683.6C367.6,684 367,684.1 366.5,683.8C366.4,683.7 366.3,683.6 366.3,683.5L356.8,668.1C356.6,667.8 356.6,667.4 356.8,667.1L366.5,651.7C366.8,651.2 367.4,651 367.9,651.3C368.1,651.4 368.2,651.6 368.3,651.8ZM384.5,681.2L393.9,697.9C394.1,698.2 394.1,698.6 393.9,699L383.9,712.8C383.5,713.3 382.9,713.4 382.5,713C382.4,713 382.3,712.9 382.2,712.7L372.2,695.7C372,695.4 372,694.9 372.2,694.6L382.9,681.1C383.2,680.7 383.8,680.6 384.3,680.9C384.4,681 384.5,681.1 384.5,681.2ZM366.1,705.7L375.1,721.7C375.3,722 375.3,722.5 375.1,722.8L366.7,734.5C366.3,734.9 365.7,735 365.3,734.7C365.1,734.6 365.1,734.5 365,734.4L355.6,718.6C355.4,718.3 355.4,717.9 355.6,717.5L364.4,705.6C364.8,705.2 365.4,705.1 365.8,705.4C366,705.5 366.1,705.6 366.1,705.7ZM349.7,728.5L358.5,743.9C358.7,744.3 358.6,744.7 358.4,745L348.2,757.5C347.9,757.9 347.2,758 346.8,757.6C346.7,757.5 346.7,757.5 346.6,757.4L338.1,743.4C337.8,743.1 337.9,742.7 338.1,742.3L348,728.4C348.3,728 349,727.9 349.4,728.2C349.5,728.3 349.6,728.4 349.7,728.5ZM332.8,751.5L341.6,766.9C341.8,767.2 341.7,767.7 341.5,768L331.3,780.4C331,780.9 330.3,780.9 329.9,780.6C329.8,780.5 329.7,780.4 329.7,780.3L321.1,766.4C320.9,766.1 321,765.6 321.2,765.3L331.1,751.4C331.4,751 332.1,750.9 332.5,751.2C332.6,751.3 332.7,751.4 332.8,751.5ZM365.8,754.4L374.8,769.2C375.1,769.6 375,770 374.7,770.4L365.2,781.8C364.8,782.3 364.2,782.3 363.8,782C363.7,781.9 363.6,781.8 363.6,781.8L354.4,768.7C354.2,768.4 354.2,767.9 354.4,767.5L364.1,754.3C364.5,753.9 365.1,753.8 365.5,754.1C365.6,754.2 365.7,754.3 365.8,754.4ZM347.5,777.5L355.2,790.5C355.4,790.9 355.4,791.4 355,791.7L341,806C340.6,806.4 339.9,806.4 339.5,806.1C339.5,806 339.4,805.9 339.4,805.9L332.9,795C332.7,794.7 332.7,794.2 333,793.9L345.9,777.4C346.2,776.9 346.9,776.9 347.3,777.2C347.4,777.3 347.5,777.4 347.5,777.5ZM382.5,778.4L391.6,793.2C391.8,793.6 391.7,794.1 391.4,794.4L379.9,805.6C379.5,805.9 378.9,805.9 378.5,805.5C378.5,805.5 378.4,805.4 378.4,805.4L369.9,792.5C369.6,792.1 369.7,791.7 369.9,791.3L380.9,778.3C381.3,777.8 381.9,777.8 382.3,778.2C382.4,778.2 382.5,778.3 382.5,778.4ZM362.8,800.1L372.2,815.1C372.4,815.5 372.4,816 372,816.3L356.5,831.6C356.1,832 355.5,832 355.1,831.6C355.1,831.5 355,831.5 355,831.4L345.8,816.7C345.5,816.3 345.6,815.8 345.9,815.5L361.3,799.9C361.6,799.5 362.3,799.5 362.7,799.9C362.7,799.9 362.8,800 362.8,800.1ZM339.2,823.5L348.1,838.7C348.3,839 348.3,839.5 348,839.8L336.6,852.2C336.2,852.6 335.5,852.7 335.1,852.3C335,852.2 334.9,852 334.9,851.8L329.3,833C329.2,832.6 329.3,832.3 329.5,832L337.6,823.4C338,823 338.6,822.9 339,823.3C339.1,823.4 339.2,823.5 339.2,823.5ZM398.7,802.3L411.4,818C411.7,818.3 411.7,818.9 411.4,819.2L400.1,832.8C399.8,833.2 399.1,833.2 398.7,832.9C398.6,832.8 398.6,832.7 398.5,832.7L387.2,815.6C386.9,815.2 386.9,814.7 387.2,814.4L397.2,802.3C397.6,801.9 398.2,801.9 398.6,802.2C398.7,802.2 398.7,802.3 398.7,802.3ZM379.9,824.5L392.7,841.6C393,842 393,842.6 392.6,842.9L376.2,860.3C375.9,860.7 375.2,860.7 374.8,860.4C374.8,860.3 374.7,860.3 374.7,860.2L362.4,842.3C362.2,841.9 362.2,841.4 362.5,841L378.4,824.4C378.7,824 379.4,824 379.8,824.3C379.8,824.4 379.9,824.4 379.9,824.5ZM354.9,849.4L367.9,867.8C368.1,868.2 368.1,868.7 367.8,869L354.2,883.8C353.9,884.2 353.2,884.3 352.8,883.9C352.7,883.8 352.7,883.7 352.6,883.6L340.7,861.1C340.4,860.7 340.5,860.2 340.9,859.9L353.5,849.2C353.9,848.9 354.5,848.9 354.9,849.4C354.9,849.4 354.9,849.4 354.9,849.4ZM333.3,868.9L345.7,890C345.9,890.4 345.9,890.8 345.6,891.1L332.9,905.6C332.5,906 331.9,906.1 331.4,905.7C331.3,905.6 331.2,905.4 331.2,905.3L321.2,878.1C321.1,877.7 321.2,877.2 321.5,876.9L331.9,868.6C332.3,868.3 332.9,868.3 333.3,868.8C333.3,868.8 333.3,868.9 333.3,868.9ZM375.3,877.4L394.8,902.6C395.1,903 395.1,903.6 394.7,904L379.7,918.1C379.3,918.5 378.6,918.5 378.2,918.1C378.2,918.1 378.2,918.1 378.2,918C374.4,913.5 371.1,909.3 368.3,905.4C365.4,901.5 362.7,897.5 360.1,893.3L360.1,893.3C359.8,893 359.9,892.5 360.2,892.1L373.8,877.4C374.2,877 374.8,876.9 375.2,877.3C375.2,877.3 375.3,877.4 375.3,877.4ZM419.8,826.3L439.6,848.5C440,848.8 440,849.4 439.7,849.7L427.7,866.7C427.4,867.1 426.8,867.2 426.3,866.9C426.2,866.9 426.2,866.8 426.1,866.8L406.3,842.7C406,842.4 406,841.9 406.2,841.5L418.2,826.4C418.6,825.9 419.2,825.8 419.6,826.2C419.7,826.2 419.7,826.3 419.8,826.3ZM399.7,850.4L419.8,875C420.2,875.4 420.1,876 419.8,876.3L402.8,895.5C402.5,895.9 401.8,895.9 401.4,895.5C401.4,895.5 401.3,895.5 401.3,895.4L381.5,871.2C381.2,870.8 381.2,870.3 381.5,869.9L398.2,850.4C398.5,849.9 399.2,849.9 399.6,850.2C399.6,850.3 399.7,850.3 399.7,850.4ZM352.1,901.4L372,925.7C372.3,926.2 372.3,926.8 371.9,927.1L354.1,942.8C353.7,943.2 353.1,943.1 352.7,942.7C352.7,942.7 352.6,942.6 352.6,942.6L337.1,916.2C336.8,915.8 336.9,915.3 337.2,915L350.6,901.3C351,900.9 351.6,900.9 352,901.3C352.1,901.3 352.1,901.3 352.1,901.4ZM402.1,911.2L424,934C424.4,934.4 424.4,935 424,935.4C424,935.4 424,935.5 423.9,935.5L408.7,946.7C408.3,947 407.8,947 407.4,946.7L386.4,927.1C386,926.7 385.9,926.1 386.3,925.7C386.3,925.7 386.3,925.7 386.3,925.7L400.7,911.2C401.1,910.8 401.7,910.8 402.1,911.2C402.1,911.2 402.1,911.2 402.1,911.2ZM449.7,856.1L473.3,876C473.7,876.4 473.8,876.9 473.5,877.3L460.8,900.8C460.5,901.3 459.9,901.5 459.4,901.2C459.3,901.2 459.3,901.1 459.2,901L434.2,875.7C433.8,875.3 433.8,874.8 434.1,874.4L448.3,856.3C448.6,855.8 449.3,855.8 449.7,856.1C449.7,856.1 449.7,856.1 449.7,856.1ZM428.8,884.3L460.8,915.5C461.1,915.8 461.2,916.2 461.1,916.6L451.9,944.8C451.7,945.3 451.2,945.6 450.6,945.5C450.5,945.4 450.4,945.3 450.3,945.2L409.3,905.4C408.9,905 408.9,904.4 409.3,904L427.4,884.3C427.8,883.9 428.4,883.9 428.8,884.3C428.8,884.3 428.8,884.3 428.8,884.3ZM382.7,733.4L392.7,748.5C392.9,748.8 392.9,749.3 392.6,749.7L383.3,760.9C382.9,761.3 382.3,761.4 381.9,761C381.8,761 381.7,760.9 381.7,760.8L372.2,746.4C372,746.1 372,745.6 372.2,745.3L381.1,733.3C381.4,732.9 382,732.8 382.5,733.1C382.6,733.2 382.7,733.3 382.7,733.4ZM399.7,758.3L409.4,771.5C409.7,771.9 409.7,772.4 409.4,772.8L400.1,784.2C399.8,784.6 399.2,784.7 398.7,784.3C398.7,784.3 398.6,784.2 398.6,784.2L389.2,771.5C389,771.2 389,770.7 389.2,770.3L398.1,758.3C398.4,757.9 399.1,757.8 399.5,758.1C399.6,758.2 399.7,758.3 399.7,758.3ZM417.6,781.3L429,796C429.3,796.3 429.3,796.8 429,797.2L420.1,809.8C419.8,810.3 419.1,810.4 418.7,810.1C418.6,810 418.5,810 418.5,809.9L406.1,793.7C405.8,793.3 405.9,792.7 406.2,792.4L416.1,781.2C416.5,780.8 417.1,780.8 417.5,781.1C417.5,781.2 417.6,781.2 417.6,781.3ZM436.8,805.2L456,824.5C456.4,824.8 456.4,825.3 456.2,825.7L447.7,840.8C447.5,841.3 446.8,841.5 446.4,841.2C446.3,841.2 446.2,841.1 446.1,841L425.9,818.7C425.5,818.4 425.5,817.8 425.8,817.5L435.3,805.3C435.6,804.9 436.2,804.8 436.7,805.2C436.7,805.2 436.7,805.2 436.8,805.2ZM464.5,831.8L482.1,843.5C482.6,843.8 482.7,844.4 482.4,844.9L471.9,861.6C471.6,862 471,862.2 470.6,861.9C470.5,861.8 470.5,861.8 470.4,861.8L454.3,847.8C453.9,847.5 453.9,846.9 454.1,846.5L463.1,832.1C463.4,831.6 464,831.5 464.5,831.8C464.5,831.8 464.5,831.8 464.5,831.8ZM400.7,708.8L411.8,725.1C412.1,725.4 412.1,725.9 411.8,726.3L400.8,739.7C400.5,740.1 399.9,740.2 399.4,739.9C399.3,739.8 399.3,739.7 399.2,739.6L389.1,723C388.9,722.7 389,722.2 389.2,721.9L399.1,708.8C399.4,708.3 400.1,708.2 400.5,708.6C400.6,708.6 400.7,708.7 400.7,708.8ZM418.8,734.4L428.5,746.9C428.8,747.2 428.8,747.7 428.6,748.1L417.2,763.3C416.8,763.7 416.2,763.8 415.8,763.5C415.7,763.4 415.6,763.3 415.5,763.2L406.7,750.5C406.5,750.2 406.5,749.7 406.7,749.4L417.2,734.5C417.5,734 418.1,733.9 418.6,734.2C418.6,734.3 418.7,734.4 418.8,734.4ZM436.3,756.2L447.8,768.7C448.1,769.1 448.1,769.6 447.9,770L436.2,786.8C435.9,787.2 435.3,787.3 434.8,787C434.7,786.9 434.7,786.9 434.6,786.8L423.9,773.1C423.6,772.7 423.6,772.3 423.8,771.9L434.7,756.3C435,755.9 435.7,755.8 436.1,756.1C436.2,756.1 436.2,756.2 436.3,756.2ZM456.6,778.1L473.9,793.1C474.3,793.4 474.4,793.9 474.1,794.4L462.1,814.8C461.8,815.3 461.2,815.4 460.7,815.1C460.6,815.1 460.5,815 460.5,814.9L443.4,796.5C443,796.1 443,795.6 443.3,795.2L455.1,778.3C455.5,777.9 456.1,777.8 456.5,778.1C456.6,778.1 456.6,778.1 456.6,778.1ZM350.6,678.3L359.7,693.8C359.9,694.2 359.9,694.6 359.6,694.9L350.3,707.4C350,707.8 349.4,707.9 348.9,707.6C348.8,707.5 348.7,707.4 348.7,707.3L340.2,693.4C340.1,693.1 340.1,692.7 340.3,692.4L348.9,678.3C349.2,677.9 349.8,677.7 350.3,678C350.4,678.1 350.6,678.2 350.6,678.3ZM334.5,704.4L342.4,716.7C342.7,717 342.6,717.5 342.4,717.8L333,732.1C332.7,732.5 332,732.7 331.6,732.4C331.4,732.3 331.3,732.1 331.2,731.9L324.9,717.7C324.7,717.4 324.7,717.1 324.9,716.8L332.9,704.4C333.2,703.9 333.8,703.8 334.2,704.1C334.4,704.1 334.5,704.3 334.5,704.4ZM424.9,520.5L434.5,534.3C434.7,534.6 434.7,535.1 434.5,535.4L424.9,550.5C424.6,551 424,551.1 423.5,550.8C423.4,550.7 423.3,550.6 423.2,550.4L414.6,535.4C414.4,535 414.4,534.6 414.6,534.3L423.2,520.6C423.5,520.1 424.1,520 424.6,520.3C424.7,520.3 424.8,520.4 424.9,520.5ZM408.4,546.5L417.1,559.7C417.3,560 417.3,560.5 417,560.8L405.5,576.7C405.2,577.1 404.6,577.2 404.1,576.9C404,576.8 404,576.7 403.9,576.6L395.8,564.1C395.6,563.8 395.6,563.4 395.8,563L406.7,546.5C407,546 407.6,545.9 408.1,546.2C408.2,546.2 408.3,546.3 408.4,546.5ZM390.6,573.6L398.1,587.5C398.3,587.8 398.3,588.2 398.1,588.5L387.6,602.5C387.3,602.9 386.7,603 386.2,602.7C386.1,602.6 386,602.5 385.9,602.3L378.7,588.6C378.6,588.3 378.6,587.9 378.8,587.6L388.9,573.5C389.2,573 389.9,572.9 390.3,573.2C390.4,573.3 390.5,573.4 390.6,573.6ZM373,600.5L379.7,612.6C379.9,612.9 379.9,613.3 379.7,613.6L369.2,629.4C368.9,629.9 368.3,630 367.8,629.7C367.6,629.6 367.5,629.5 367.4,629.3L361.3,615.5C361.2,615.1 361.2,614.8 361.4,614.5L371.3,600.4C371.6,600 372.3,599.9 372.7,600.2C372.8,600.3 372.9,600.4 373,600.5ZM354.5,627.7L361.5,639.6C361.7,640 361.6,640.4 361.4,640.7L351.6,655.2C351.3,655.7 350.7,655.8 350.2,655.5C350.1,655.4 349.9,655.2 349.9,655.1L344,642C343.8,641.7 343.9,641.3 344,641L352.8,627.7C353.1,627.2 353.8,627.1 354.2,627.4C354.4,627.5 354.5,627.6 354.5,627.7ZM337.7,653.5L344.4,665.1C344.6,665.4 344.6,665.9 344.4,666.2L335,680.7C334.7,681.1 334.1,681.3 333.6,681C333.4,680.9 333.3,680.7 333.2,680.6L327.1,667.8C327,667.5 327,667.1 327.2,666.8L336,653.5C336.3,653 336.9,652.9 337.3,653.2C337.5,653.3 337.6,653.4 337.7,653.5ZM408,494.4L418,510C418.3,510.3 418.3,510.8 418,511.1L409.2,525C408.9,525.4 408.3,525.6 407.8,525.3C407.6,525.2 407.5,525 407.4,524.9L398.4,508.2C398.3,507.9 398.3,507.5 398.5,507.2L406.3,494.4C406.6,494 407.2,493.8 407.7,494.1C407.8,494.2 407.9,494.3 408,494.4ZM391.7,520.5L401.7,536C401.9,536.4 401.9,536.8 401.7,537.1L391.7,551.7C391.4,552.2 390.8,552.3 390.3,552C390.2,551.9 390.1,551.8 390,551.6L381.1,536.2C381,535.9 381,535.5 381.2,535.2L390,520.5C390.3,520 390.9,519.9 391.4,520.2C391.5,520.2 391.6,520.3 391.7,520.5ZM374.6,546.6L383,561.6C383.2,561.9 383.2,562.2 383,562.6L374.5,577.1C374.2,577.6 373.6,577.8 373.1,577.5C373,577.4 372.8,577.2 372.7,577.1L364.4,560.9C364.3,560.6 364.3,560.2 364.5,559.9L372.9,546.5C373.2,546.1 373.8,545.9 374.3,546.2C374.4,546.3 374.5,546.4 374.6,546.6ZM358.6,572.7L366.7,587.5C366.8,587.8 366.8,588.2 366.6,588.6L356.3,603.5C356,604 355.4,604.1 354.9,603.8C354.8,603.6 354.6,603.5 354.6,603.3L348,588.2C347.9,587.9 347.9,587.6 348.1,587.3L356.9,572.6C357.2,572.1 357.8,572 358.3,572.3C358.4,572.4 358.5,572.5 358.6,572.7ZM341.8,600.7L348.8,614.3C348.9,614.6 348.9,615 348.7,615.3L339.4,629.1C339.1,629.5 338.5,629.7 338,629.3C337.9,629.2 337.7,629.1 337.7,628.9L332.3,617.4C332.1,617.1 332.1,616.8 332.3,616.5L340,600.8C340.2,600.3 340.8,600.1 341.3,600.3C341.5,600.4 341.7,600.6 341.8,600.7ZM326.8,628.2L331.7,640.2C331.8,640.5 331.8,640.8 331.6,641.1L324.8,652.5C324.5,652.9 323.9,653.1 323.4,652.8C323.2,652.7 323,652.5 323,652.3L319.1,641.6C319,641.4 319,641.1 319.2,640.9L324.9,628.1C325.1,627.6 325.7,627.4 326.2,627.6C326.5,627.7 326.7,627.9 326.8,628.2ZM456.2,475.5L465.4,491.4C465.6,491.7 465.6,492.1 465.4,492.4L461,501.2C460.8,501.7 460.2,501.9 459.7,501.7C459.5,501.6 459.4,501.5 459.3,501.3L448.2,485.7C447.9,485.3 447.9,484.9 448.2,484.5L454.5,475.4C454.8,475 455.4,474.9 455.9,475.2C456,475.3 456.1,475.4 456.2,475.5ZM454.8,377L464.8,389.9C465,390.2 465.1,390.7 464.9,391L457.6,403.8C457.4,404.3 456.8,404.5 456.3,404.2C456.1,404.1 456,404 455.9,403.9L446.2,388.8C446,388.5 446,388.1 446.2,387.7L453.2,377.1C453.5,376.6 454.1,376.5 454.6,376.8C454.7,376.9 454.7,376.9 454.8,377ZM335.8,527.4L340.4,535.7C340.5,536 340.5,536.3 340.4,536.6L334.4,550.6C334.2,551.1 333.6,551.3 333.1,551.1C332.9,551 332.8,550.9 332.7,550.8L327.3,544.3C327.1,544 327,543.6 327.1,543.3L334,527.4C334.2,526.9 334.8,526.7 335.3,526.9C335.5,527 335.7,527.2 335.8,527.4ZM347.5,549.6L351.8,560.2C351.9,560.5 351.9,560.8 351.7,561.1L343.6,575.4C343.3,575.9 342.7,576 342.2,575.7C342,575.6 341.8,575.5 341.8,575.2L337.5,564.6C337.4,564.3 337.4,564 337.6,563.7L345.7,549.5C346,549 346.6,548.9 347.1,549.1C347.3,549.3 347.5,549.4 347.5,549.6ZM333.1,579.6L336.3,586.3C336.5,586.6 336.5,587 336.3,587.2L327.2,604.1C326.9,604.5 326.3,604.7 325.8,604.5C325.6,604.3 325.4,604.1 325.3,603.8L322.9,594.8C322.9,594.6 322.9,594.3 323,594.1L331.3,579.5C331.6,579 332.2,578.8 332.6,579.1C332.8,579.2 333,579.4 333.1,579.6ZM451.8,328L454.5,336.3C454.6,336.6 454.6,336.8 454.5,337.1L451.8,343.5C451.5,344 450.9,344.2 450.4,344C450.3,343.9 450.2,343.9 450.1,343.7L444.3,337.3C444,336.9 444,336.4 444.3,336.1L450,327.8C450.3,327.3 450.9,327.2 451.4,327.5C451.6,327.6 451.7,327.8 451.8,328ZM371.5,371.1L375.8,382.7C375.9,383 375.9,383.3 375.7,383.5L367.1,398.1C366.8,398.6 366.2,398.7 365.7,398.5C365.6,398.4 365.5,398.2 365.4,398.1L361.3,390.4C361.2,390.1 361.2,389.8 361.3,389.5L369.7,371C369.9,370.5 370.5,370.3 371,370.5C371.3,370.6 371.5,370.8 371.5,371.1ZM384.2,394.2L388.3,404.9C388.4,405.2 388.4,405.5 388.3,405.8L379.7,421.1C379.4,421.6 378.8,421.8 378.3,421.5C378.2,421.4 378,421.3 377.9,421.1L372.3,410.3C372.1,409.9 372.1,409.6 372.3,409.3L382.4,394C382.7,393.5 383.4,393.4 383.8,393.7C384,393.8 384.1,394 384.2,394.2ZM356.4,402.3L359.7,409.1C359.8,409.4 359.8,409.7 359.6,410L353.9,419.8C353.6,420.2 353,420.4 352.6,420.1C352.4,420 352.2,419.8 352.1,419.6L349.8,413.9C349.7,413.7 349.7,413.4 349.8,413.2L354.6,402.3C354.8,401.8 355.4,401.6 355.9,401.8C356.2,401.9 356.3,402.1 356.4,402.3ZM367.6,422.3L372.4,432.9C372.5,433.2 372.5,433.5 372.3,433.8L366.6,445C366.3,445.5 365.7,445.7 365.2,445.4C365.1,445.3 364.9,445.2 364.8,445L359.1,433.9C358.9,433.6 359,433.2 359.1,432.9L365.8,422.2C366.1,421.7 366.7,421.6 367.2,421.9C367.3,422 367.5,422.1 367.6,422.3ZM353.6,445.7L358.4,456.2C358.5,456.5 358.5,456.8 358.3,457.1L350.8,471.2C350.5,471.6 349.9,471.8 349.4,471.6C349.2,471.5 349,471.3 349,471.1L344.4,460.1C344.3,459.8 344.3,459.5 344.5,459.2L351.8,445.7C352.1,445.2 352.7,445 353.2,445.3C353.4,445.4 353.5,445.5 353.6,445.7ZM339.2,473.8L342.9,483.7C343,483.9 343,484.2 342.9,484.4L336.4,499.9C336.2,500.4 335.6,500.7 335.1,500.5C334.9,500.4 334.8,500.3 334.6,500.1L328.6,491.6C328.4,491.3 328.4,490.8 328.6,490.5L337.4,473.7C337.7,473.2 338.3,473.1 338.7,473.3C339,473.4 339.1,473.6 339.2,473.8ZM344.8,427.9L347.2,432.2C347.3,432.5 347.3,432.9 347.1,433.2L340.5,444.7C340.2,445.2 339.6,445.4 339.1,445.1C339,445.1 339,445 338.9,444.9L337.7,443.6C337.4,443.3 337.3,442.9 337.5,442.6L343,428.1C343.2,427.5 343.8,427.3 344.3,427.5C344.5,427.6 344.7,427.7 344.8,427.9ZM357.2,341.7L364.4,358.1C364.5,358.4 364.5,358.7 364.4,359L355.9,375.4C355.7,375.9 355.1,376.1 354.6,375.9C354.4,375.8 354.2,375.6 354.1,375.4L347.4,359.6C347.3,359.3 347.3,359 347.4,358.7L355.3,341.7C355.6,341.2 356.2,341 356.7,341.2C356.9,341.3 357.1,341.5 357.2,341.7ZM343,375.1L350.1,389C350.2,389.3 350.2,389.6 350.1,389.9L345.1,399.1C344.8,399.6 344.2,399.8 343.7,399.5C343.5,399.5 343.4,399.3 343.3,399.1L337,386.5C336.8,386.2 336.8,386 336.9,385.7L341.1,375.2C341.3,374.7 341.9,374.4 342.4,374.6C342.7,374.7 342.8,374.9 343,375.1ZM422.8,273.3L433.2,290.9C433.3,291.2 433.3,291.6 433.1,291.9L426,303.8C425.7,304.3 425.1,304.5 424.6,304.2C424.5,304.1 424.4,304 424.3,303.9L413.6,287.7C413.4,287.4 413.3,287.1 413.5,286.8C414.6,284.2 415.8,281.9 417.1,279.8C418.3,277.9 419.6,275.8 421.1,273.3L421.1,273.3C421.4,272.9 422,272.7 422.4,273C422.6,273.1 422.7,273.2 422.8,273.3ZM392.8,271.6L400.5,286.3C400.7,286.5 400.7,286.8 400.6,287.1L397.8,294.3C397.6,294.8 397,295 396.5,294.8C396.2,294.7 396,294.5 395.9,294.3L388.5,277.4C388.4,277.1 388.4,276.8 388.5,276.6L391.1,271.6C391.3,271.1 391.9,270.9 392.4,271.2C392.6,271.3 392.7,271.4 392.8,271.6ZM442,300.7L447,311.4C447.1,311.7 447.1,312.1 446.9,312.4L438.1,325.5C437.8,326 437.2,326.1 436.7,325.8C436.6,325.7 436.5,325.6 436.4,325.5L431.2,315.9C431.1,315.6 431.1,315.2 431.3,314.9L440.3,300.5C440.6,300.1 441.2,299.9 441.7,300.2C441.8,300.3 442,300.5 442,300.7ZM397,321.7L404.2,336.5C404.4,336.8 404.4,337.2 404.2,337.4L399,346.4C398.7,346.8 398.1,347 397.6,346.7C397.5,346.6 397.3,346.5 397.2,346.3L389.7,332.8C389.6,332.5 389.6,332.1 389.7,331.8L395.2,321.7C395.5,321.2 396.1,321 396.6,321.3C396.7,321.4 396.9,321.6 397,321.7ZM438.4,256.6L445.6,271.3C445.7,271.6 445.7,272 445.6,272.3L440.4,281.2C440.1,281.7 439.5,281.8 439,281.5C438.8,281.5 438.7,281.3 438.6,281.2L431.1,267.6C431,267.3 431,266.9 431.1,266.6L436.6,256.5C436.8,256 437.4,255.9 437.9,256.1C438.1,256.2 438.3,256.4 438.4,256.6ZM402.9,254.5L412,265.4C412.3,265.7 412.3,266.1 412.2,266.5L407.3,277.5C407.1,278 406.5,278.3 406,278.1C405.8,278 405.6,277.8 405.5,277.6L399.3,266.1C399.2,265.9 399.2,265.7 399.2,265.4L401.2,254.9C401.3,254.4 401.8,254 402.3,254.1C402.6,254.2 402.8,254.3 402.9,254.5ZM407.8,298.9L418.1,314.8C418.3,315.2 418.3,315.6 418,316L411.6,324.8C411.3,325.2 410.6,325.3 410.2,325C410.1,324.9 410,324.8 409.9,324.7L401.6,308.3C401.5,308 401.5,307.7 401.7,307.4L406.1,299C406.3,298.5 406.9,298.3 407.4,298.6C407.6,298.7 407.7,298.8 407.8,298.9Z"
        android:strokeWidth="1"
        android:fillColor="#22AD8E"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M495.4,229.9C509.7,221.2 527.1,216.8 547.5,216.8C568,216.8 585,223 598.5,235.2L598.5,235.2C599,235.6 599.2,236.2 599.2,236.8C587.6,418.7 581.8,514.8 581.8,525.4C581.8,541.2 612.1,758 612.1,784.2C612.1,801.6 616,850.3 623.6,930.4L494.3,930.4C501.1,844.4 504.4,795.7 504.4,784.2C504.4,766.9 519.4,552.1 519.4,531.9C519.4,518.5 511.1,418.5 494.4,231.8L494.4,231.8C494.4,231.1 494.7,230.3 495.4,229.9Z"
      android:strokeWidth="1"
      android:fillColor="#87D7E1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <group>
    <clip-path
        android:pathData="M495.4,229.9C509.7,221.2 527.1,216.8 547.5,216.8C568,216.8 585,223 598.5,235.2L598.5,235.2C599,235.6 599.2,236.2 599.2,236.8C587.6,418.7 581.8,514.8 581.8,525.4C581.8,541.2 612.1,758 612.1,784.2C612.1,801.6 616,850.3 623.6,930.4L494.3,930.4C501.1,844.4 504.4,795.7 504.4,784.2C504.4,766.9 519.4,552.1 519.4,531.9C519.4,518.5 511.1,418.5 494.4,231.8L494.4,231.8C494.4,231.1 494.7,230.3 495.4,229.9Z"/>
    <path
        android:pathData="M512.7,911L530.1,880.9L514.8,854.5L499.3,881.3L495,878.8L511.9,849.5L471,778.6L475.3,776.1L496.1,812.1L512.3,784L492.1,748.9L496.4,746.4L515.2,779L531.5,750.9L517.9,727.4L503.3,752.6L499,750.1L515,722.4L501.1,698.3L505.4,695.8L517.9,717.4L533.7,690L518.7,664.1L506.3,685.6L502,683.1L515.8,659.1L501.9,634.9L506.2,632.4L518.7,654.1L533.3,628.9L519.7,605.3L514.3,614.6L510,612.1L516.8,600.3L510.7,589.6L515,587.1L519.7,595.3L535.9,567.2L519.8,539.3L517.3,543.6L513,541.1L534.6,503.7L521,480.1L514.3,491.6L510,489.1L518.1,475.1L509.9,460.9L514.2,458.4L521,470.1L536.8,442.7L522.1,417.3L510.3,437.6L506,435.1L519.2,412.3L505.6,388.7L505,389.6L500.7,387.1L502.7,383.7L500.8,380.4L505.2,377.9L505.6,378.7L520.1,353.6L505.4,328.2L495.4,345.6L491.1,343.1L502.6,323.2L497.2,314L501.6,311.5L505.4,318.2L520.1,292.8L505,266.5L494.7,283.6L494.7,275.1L502.3,261.9L486.1,233.8L490.4,231.3L505.2,256.9L520.4,230.8L514.8,221.2L519.1,218.7L523.3,225.8L529.8,214.6L535,216.6L526.3,231.1L541,256.6L556.2,230.4L549,218L553.3,215.5L559,225.4L565.1,215L569.4,217.5L561.9,230.4L576.6,255.7L589.3,233.7L580.5,219.1L585.7,217L593,229.6L596.3,231.5L595.2,233.4L620.8,277.5L620.8,286.1L592.2,238.6L579.4,260.7L593.1,284.3L597.3,277L601.6,279.5L595.9,289.3L628.4,345.6L624.1,348.1L593.1,294.3L577.9,320.5L592.6,346L601.3,331L605.6,333.5L595.5,351L617.8,389.6L613.5,392.1L592.6,356L577.5,382.2L591.1,405.8L604.3,383L608.6,385.5L594,410.8L609.5,437.6L605.2,440.1L591.1,415.8L576.4,441.2L592.6,469.1L601.3,454L605.6,456.5L595.4,474.1L605.5,491.6L601.2,494.1L592.6,479.1L578.1,504.2L591.7,527.8L593.3,525L597.6,527.5L594.6,532.8L602.5,546.6L598.2,549.1L591.7,537.8L575.8,565.2L591.8,592.9L594.6,594.5L593.7,596.1L605.5,616.6L601.2,619.1L590.8,601.1L576,626.7L589.6,650.2L601.3,630L605.6,632.5L592.5,655.2L613.5,691.6L609.2,694.1L589.6,660.2L573.4,688.3L589.2,715.6L602.1,693.3L606.4,695.8L592.1,720.6L607.3,747L609.1,744L613.4,746.5L610.2,752L616.2,762.4L611.8,764.9L607.3,757L592.7,782.3L607.5,807.8L627.2,773.6L631.5,776.1L610.3,812.8L612.3,816.2L608,818.7L607.5,817.8L591.7,845.1L608.5,874.4L624.1,847.3L628.5,849.8L611.4,879.4L629.1,910.1L624.8,912.6L608.5,884.4L592.3,912.5L603.5,931.9L599.2,934.4L589.4,917.5L568.3,954L564,951.5L569.9,941.3L566.2,943.4L550.5,916.4L536.3,941L532,938.5L547.7,911.4L532.9,885.9L515.6,916L537.5,953.9L533.2,956.4L512.7,921L504.1,935.8L499.8,933.3L509.8,916L473,852.3L477.4,849.8L512.7,911ZM570.3,940.6L586.5,912.5L569.6,883.3L553.4,911.4L570.3,940.6ZM550.5,906.4L566.8,878.3L552,852.8L535.8,880.9L550.5,906.4ZM532.9,875.9L549.2,847.8L533.9,821.4L517.7,849.5L532.9,875.9ZM514.8,844.5L531,816.4L515.2,789L499.5,816.3L498.1,815.5L514.8,844.5ZM518.1,784L533.9,811.4L550.2,783.3L534.4,755.9L518.1,784ZM536.8,816.4L552,842.8L568.3,814.7L553,788.3L536.8,816.4ZM554.9,847.8L569.6,873.3L585.9,845.1L571.2,819.7L554.9,847.8ZM572.5,878.3L589.4,907.5L605.6,879.4L588.8,850.1L572.5,878.3ZM588.8,840.1L604.6,812.8L589.9,787.3L574.1,814.7L588.8,840.1ZM571.2,809.7L587,782.3L571.7,755.9L555.9,783.3L571.2,809.7ZM553,778.3L568.8,750.9L553,723.6L537.3,750.9L553,778.3ZM534.4,745.9L550.2,718.6L536.6,695L520.8,722.4L534.4,745.9ZM521.6,659.1L536.6,685L551.2,659.8L536.2,633.9L521.6,659.1ZM539.5,690L553,713.6L567.6,688.3L554,664.8L539.5,690ZM555.9,718.6L571.7,745.9L586.3,720.6L570.5,693.3L555.9,718.6ZM574.6,750.9L589.9,777.3L604.4,752L589.2,725.6L574.6,750.9ZM570.5,683.3L586.7,655.2L573.2,631.7L556.9,659.8L570.5,683.3ZM554,654.8L570.3,626.7L555.3,600.8L539.1,628.9L554,654.8ZM536.2,623.9L552.4,595.8L538.8,572.2L522.6,600.3L536.2,623.9ZM522.7,534.3L538.8,562.2L553.6,536.6L537.5,508.7L522.7,534.3ZM541.7,567.2L555.3,590.8L570.1,565.2L556.5,541.6L541.7,567.2ZM558.2,595.8L573.2,621.7L587.9,596.1L573,570.2L558.2,595.8ZM573,560.2L588.8,532.8L575.2,509.2L559.4,536.6L573,560.2ZM556.5,531.6L572.3,504.2L556.2,476.3L540.4,503.7L556.5,531.6ZM537.5,498.7L553.3,471.3L539.7,447.7L523.8,475.1L537.5,498.7ZM508.5,383.7L522.1,407.3L536.6,382.2L523,358.6L508.5,383.7ZM525,412.3L539.7,437.7L554.2,412.6L539.5,387.2L525,412.3ZM542.6,442.7L556.2,466.3L570.7,441.2L557,417.6L542.6,442.7ZM559.1,471.3L575.2,499.2L589.7,474.1L573.6,446.2L559.1,471.3ZM573.6,436.2L588.3,410.8L574.6,387.2L559.9,412.6L573.6,436.2ZM557,407.6L571.7,382.2L557,356.7L542.3,382.2L557,407.6ZM539.5,377.2L554.2,351.7L540.5,328.1L525.8,353.6L539.5,377.2ZM523,348.6L537.7,323.1L523,297.8L508.3,323.2L523,348.6ZM507.9,261.6L523,287.8L538.1,261.6L523.4,236L507.9,261.6ZM525.9,292.8L540.5,318.1L555.7,291.9L541,266.6L525.9,292.8ZM543.4,323.1L557,346.7L572.2,320.5L558.5,296.9L543.4,323.1ZM559.9,351.7L574.6,377.2L589.7,351L575,325.5L559.9,351.7ZM575,315.5L590.2,289.3L576.6,265.7L561.4,291.9L575,315.5ZM558.5,286.9L573.7,260.7L559,235.4L543.9,261.6L558.5,286.9Z"
        android:strokeWidth="1"
        android:fillColor="#4A949B"
        android:fillType="evenOdd"
        android:strokeColor="#00000000"/>
  </group>
  <path
      android:pathData="M660.6,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M665.3,265.7l96.8,0l-25.4,149.3l0,515l-90,0l0,-515z"
      android:strokeWidth="1"
      android:fillColor="#BCD9DF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M761.7,266.2l-25.6,148.8l0,515.3l54,0l0,-515.3z"
      android:strokeWidth="1"
      android:fillColor="#A3C7CF"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M759,292.5l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M756,307l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M756,321l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M763,321l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M768,335l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M760,335l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M752,335l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M763,307l5,0l0,8.5l-5,0z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M750,348L755,348L755,356.5L750,356.5L750,348ZM757,348L762,348L762,356.5L757,356.5L757,348ZM764,348L769,348L769,356.5L764,356.5L764,348ZM771,348L776,348L776,356.5L771,356.5L771,348Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M750,362.5L755,362.5L755,371.1L750,371.1L750,362.5ZM757,362.5L762,362.5L762,371.1L757,371.1L757,362.5ZM764,362.5L769,362.5L769,371.1L764,371.1L764,362.5ZM771,362.5L776,362.5L776,371.1L771,371.1L771,362.5Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M746,375.5L751,375.5L751,384.1L746,384.1L746,375.5ZM753,375.5L758,375.5L758,384.1L753,384.1L753,375.5ZM760,375.5L765,375.5L765,384.1L760,384.1L760,375.5ZM767,375.5L772,375.5L772,384.1L767,384.1L767,375.5ZM775,375.5L780,375.5L780,384.1L775,384.1L775,375.5Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M746,390.5L751,390.5L751,399.1L746,399.1L746,390.5ZM753,390.5L758,390.5L758,399.1L753,399.1L753,390.5ZM760,390.5L765,390.5L765,399.1L760,399.1L760,390.5ZM767,390.5L772,390.5L772,399.1L767,399.1L767,390.5ZM775,390.5L780,390.5L780,399.1L775,399.1L775,390.5Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M738.5,895.4L744.3,895.9L743.8,904.4L738,903.9L738.5,895.4ZM747.3,895.4L753.1,895.9L752.5,904.4L746.8,903.9L747.3,895.4ZM756,895.4L761.8,895.9L761.3,904.4L755.5,903.9L756,895.4ZM764.8,895.4L770.5,895.9L770,904.4L764.2,903.9L764.8,895.4ZM773.5,895.4L779.3,895.9L778.7,904.4L773,903.9L773.5,895.4ZM782.2,895.4L788,895.9L787.5,904.4L781.7,903.9L782.2,895.4Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,882L745.3,882.5L744.8,891L739,890.5L739.5,882ZM748.3,882L754,882.5L753.5,891L747.7,890.5L748.3,882ZM757,882L762.8,882.5L762.2,891L756.5,890.5L757,882ZM765.7,882L771.5,882.5L771,891L765.2,890.5L765.7,882ZM774.5,882L780.2,882.5L779.7,891L773.9,890.5L774.5,882ZM783.2,882L789,882.5L788.5,891L782.7,890.5L783.2,882Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M738.5,867L744.3,867.5L743.8,876L738,875.5L738.5,867ZM747.3,867L753,867.5L752.5,876L746.7,875.5L747.3,867ZM756,867L761.8,867.5L761.2,876L755.5,875.5L756,867ZM764.7,867L770.5,867.5L770,876L764.2,875.5L764.7,867ZM773.5,867L779.2,867.5L778.7,876L772.9,875.5L773.5,867ZM782.2,867L788,867.5L787.5,876L781.7,875.5L782.2,867Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,854L745.3,854.5L744.8,863L739,862.5L739.5,854ZM748.3,854L754,854.5L753.5,863L747.7,862.5L748.3,854ZM757,854L762.8,854.5L762.2,863L756.5,862.5L757,854ZM765.7,854L771.5,854.5L771,863L765.2,862.5L765.7,854ZM774.5,854L780.2,854.5L779.7,863L773.9,862.5L774.5,854ZM783.2,854L789,854.5L788.5,863L782.7,862.5L783.2,854Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,839L745.3,839.5L744.8,848L739,847.5L739.5,839ZM748.3,839L754,839.5L753.5,848L747.7,847.5L748.3,839ZM757,839L762.8,839.5L762.2,848L756.5,847.5L757,839ZM765.7,839L771.5,839.5L771,848L765.2,847.5L765.7,839ZM774.5,839L780.2,839.5L779.7,848L773.9,847.5L774.5,839ZM783.2,839L789,839.5L788.5,848L782.7,847.5L783.2,839Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,826L745.3,826.5L744.8,835L739,834.5L739.5,826ZM748.3,826L754,826.5L753.5,835L747.7,834.5L748.3,826ZM757,826L762.8,826.5L762.2,835L756.5,834.5L757,826ZM765.7,826L771.5,826.5L771,835L765.2,834.5L765.7,826ZM774.5,826L780.2,826.5L779.7,835L773.9,834.5L774.5,826ZM783.2,826L789,826.5L788.5,835L782.7,834.5L783.2,826Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,811L745.3,811.5L744.8,820L739,819.5L739.5,811ZM748.3,811L754,811.5L753.5,820L747.7,819.5L748.3,811ZM757,811L762.8,811.5L762.2,820L756.5,819.5L757,811ZM765.7,811L771.5,811.5L771,820L765.2,819.5L765.7,811ZM774.5,811L780.2,811.5L779.7,820L773.9,819.5L774.5,811ZM783.2,811L789,811.5L788.5,820L782.7,819.5L783.2,811Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,798L745.3,798.5L744.8,807L739,806.5L739.5,798ZM748.3,798L754,798.5L753.5,807L747.7,806.5L748.3,798ZM757,798L762.8,798.5L762.2,807L756.5,806.5L757,798ZM765.7,798L771.5,798.5L771,807L765.2,806.5L765.7,798ZM774.5,798L780.2,798.5L779.7,807L773.9,806.5L774.5,798ZM783.2,798L789,798.5L788.5,807L782.7,806.5L783.2,798Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,783L745.3,783.5L744.8,792L739,791.5L739.5,783ZM748.3,783L754,783.5L753.5,792L747.7,791.5L748.3,783ZM757,783L762.8,783.5L762.2,792L756.5,791.5L757,783ZM765.7,783L771.5,783.5L771,792L765.2,791.5L765.7,783ZM774.5,783L780.2,783.5L779.7,792L773.9,791.5L774.5,783ZM783.2,783L789,783.5L788.5,792L782.7,791.5L783.2,783Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,770L745.3,770.5L744.8,779L739,778.5L739.5,770ZM748.3,770L754,770.5L753.5,779L747.7,778.5L748.3,770ZM757,770L762.8,770.5L762.2,779L756.5,778.5L757,770ZM765.7,770L771.5,770.5L771,779L765.2,778.5L765.7,770ZM774.5,770L780.2,770.5L779.7,779L773.9,778.5L774.5,770ZM783.2,770L789,770.5L788.5,779L782.7,778.5L783.2,770Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,755L745.3,755.5L744.8,764L739,763.5L739.5,755ZM748.3,755L754,755.5L753.5,764L747.7,763.5L748.3,755ZM757,755L762.8,755.5L762.2,764L756.5,763.5L757,755ZM765.7,755L771.5,755.5L771,764L765.2,763.5L765.7,755ZM774.5,755L780.2,755.5L779.7,764L773.9,763.5L774.5,755ZM783.2,755L789,755.5L788.5,764L782.7,763.5L783.2,755Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M740.5,742L746.3,742.5L745.8,751L740,750.5L740.5,742ZM749.3,742L755,742.5L754.5,751L748.7,750.5L749.3,742ZM758,742L763.8,742.5L763.2,751L757.5,750.5L758,742ZM766.7,742L772.5,742.5L772,751L766.2,750.5L766.7,742ZM775.5,742L781.2,742.5L780.7,751L774.9,750.5L775.5,742ZM784.2,742L790,742.5L789.5,751L783.7,750.5L784.2,742Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,727L745.3,727.5L744.8,736L739,735.5L739.5,727ZM748.3,727L754,727.5L753.5,736L747.7,735.5L748.3,727ZM757,727L762.8,727.5L762.2,736L756.5,735.5L757,727ZM765.7,727L771.5,727.5L771,736L765.2,735.5L765.7,727ZM774.5,727L780.2,727.5L779.7,736L773.9,735.5L774.5,727ZM783.2,727L789,727.5L788.5,736L782.7,735.5L783.2,727Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,714L745.3,714.5L744.8,723L739,722.5L739.5,714ZM748.3,714L754,714.5L753.5,723L747.7,722.5L748.3,714ZM757,714L762.8,714.5L762.2,723L756.5,722.5L757,714ZM765.7,714L771.5,714.5L771,723L765.2,722.5L765.7,714ZM774.5,714L780.2,714.5L779.7,723L773.9,722.5L774.5,714ZM783.2,714L789,714.5L788.5,723L782.7,722.5L783.2,714Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,699L745.3,699.5L744.8,708L739,707.5L739.5,699ZM748.3,699L754,699.5L753.5,708L747.7,707.5L748.3,699ZM757,699L762.8,699.5L762.2,708L756.5,707.5L757,699ZM765.7,699L771.5,699.5L771,708L765.2,707.5L765.7,699ZM774.5,699L780.2,699.5L779.7,708L773.9,707.5L774.5,699ZM783.2,699L789,699.5L788.5,708L782.7,707.5L783.2,699Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,686L745.3,686.5L744.8,695L739,694.5L739.5,686ZM748.3,686L754,686.5L753.5,695L747.7,694.5L748.3,686ZM757,686L762.8,686.5L762.2,695L756.5,694.5L757,686ZM765.7,686L771.5,686.5L771,695L765.2,694.5L765.7,686ZM774.5,686L780.2,686.5L779.7,695L773.9,694.5L774.5,686ZM783.2,686L789,686.5L788.5,695L782.7,694.5L783.2,686Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,671L745.3,671.5L744.8,680L739,679.5L739.5,671ZM748.3,671L754,671.5L753.5,680L747.7,679.5L748.3,671ZM757,671L762.8,671.5L762.2,680L756.5,679.5L757,671ZM765.7,671L771.5,671.5L771,680L765.2,679.5L765.7,671ZM774.5,671L780.2,671.5L779.7,680L773.9,679.5L774.5,671ZM783.2,671L789,671.5L788.5,680L782.7,679.5L783.2,671Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,658L745.3,658.5L744.8,667L739,666.5L739.5,658ZM748.3,658L754,658.5L753.5,667L747.7,666.5L748.3,658ZM757,658L762.8,658.5L762.2,667L756.5,666.5L757,658ZM765.7,658L771.5,658.5L771,667L765.2,666.5L765.7,658ZM774.5,658L780.2,658.5L779.7,667L773.9,666.5L774.5,658ZM783.2,658L789,658.5L788.5,667L782.7,666.5L783.2,658Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,643L745.3,643.5L744.8,652L739,651.5L739.5,643ZM748.3,643L754,643.5L753.5,652L747.7,651.5L748.3,643ZM757,643L762.8,643.5L762.2,652L756.5,651.5L757,643ZM765.7,643L771.5,643.5L771,652L765.2,651.5L765.7,643ZM774.5,643L780.2,643.5L779.7,652L773.9,651.5L774.5,643ZM783.2,643L789,643.5L788.5,652L782.7,651.5L783.2,643Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M740.5,630L746.3,630.5L745.8,639L740,638.5L740.5,630ZM749.3,630L755,630.5L754.5,639L748.7,638.5L749.3,630ZM758,630L763.8,630.5L763.2,639L757.5,638.5L758,630ZM766.7,630L772.5,630.5L772,639L766.2,638.5L766.7,630ZM775.5,630L781.2,630.5L780.7,639L774.9,638.5L775.5,630ZM784.2,630L790,630.5L789.5,639L783.7,638.5L784.2,630Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,615L745.3,615.5L744.8,624L739,623.5L739.5,615ZM748.3,615L754,615.5L753.5,624L747.7,623.5L748.3,615ZM757,615L762.8,615.5L762.2,624L756.5,623.5L757,615ZM765.7,615L771.5,615.5L771,624L765.2,623.5L765.7,615ZM774.5,615L780.2,615.5L779.7,624L773.9,623.5L774.5,615ZM783.2,615L789,615.5L788.5,624L782.7,623.5L783.2,615Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,602L745.3,602.5L744.8,611L739,610.5L739.5,602ZM748.3,602L754,602.5L753.5,611L747.7,610.5L748.3,602ZM757,602L762.8,602.5L762.2,611L756.5,610.5L757,602ZM765.7,602L771.5,602.5L771,611L765.2,610.5L765.7,602ZM774.5,602L780.2,602.5L779.7,611L773.9,610.5L774.5,602ZM783.2,602L789,602.5L788.5,611L782.7,610.5L783.2,602Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,585L745.3,585.5L744.8,594L739,593.5L739.5,585ZM748.3,585L754,585.5L753.5,594L747.7,593.5L748.3,585ZM757,585L762.8,585.5L762.2,594L756.5,593.5L757,585ZM765.7,585L771.5,585.5L771,594L765.2,593.5L765.7,585ZM774.5,585L780.2,585.5L779.7,594L773.9,593.5L774.5,585ZM783.2,585L789,585.5L788.5,594L782.7,593.5L783.2,585Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,572L745.3,572.5L744.8,581L739,580.5L739.5,572ZM748.3,572L754,572.5L753.5,581L747.7,580.5L748.3,572ZM757,572L762.8,572.5L762.2,581L756.5,580.5L757,572ZM765.7,572L771.5,572.5L771,581L765.2,580.5L765.7,572ZM774.5,572L780.2,572.5L779.7,581L773.9,580.5L774.5,572ZM783.2,572L789,572.5L788.5,581L782.7,580.5L783.2,572Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,557L745.3,557.5L744.8,566L739,565.5L739.5,557ZM748.3,557L754,557.5L753.5,566L747.7,565.5L748.3,557ZM757,557L762.8,557.5L762.2,566L756.5,565.5L757,557ZM765.7,557L771.5,557.5L771,566L765.2,565.5L765.7,557ZM774.5,557L780.2,557.5L779.7,566L773.9,565.5L774.5,557ZM783.2,557L789,557.5L788.5,566L782.7,565.5L783.2,557Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,544L745.3,544.5L744.8,553L739,552.5L739.5,544ZM748.3,544L754,544.5L753.5,553L747.7,552.5L748.3,544ZM757,544L762.8,544.5L762.2,553L756.5,552.5L757,544ZM765.7,544L771.5,544.5L771,553L765.2,552.5L765.7,544ZM774.5,544L780.2,544.5L779.7,553L773.9,552.5L774.5,544ZM783.2,544L789,544.5L788.5,553L782.7,552.5L783.2,544Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,529L745.3,529.5L744.8,538L739,537.5L739.5,529ZM748.3,529L754,529.5L753.5,538L747.7,537.5L748.3,529ZM757,529L762.8,529.5L762.2,538L756.5,537.5L757,529ZM765.7,529L771.5,529.5L771,538L765.2,537.5L765.7,529ZM774.5,529L780.2,529.5L779.7,538L773.9,537.5L774.5,529ZM783.2,529L789,529.5L788.5,538L782.7,537.5L783.2,529Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M740.5,516L746.3,516.5L745.8,525L740,524.5L740.5,516ZM749.3,516L755,516.5L754.5,525L748.7,524.5L749.3,516ZM758,516L763.8,516.5L763.2,525L757.5,524.5L758,516ZM766.7,516L772.5,516.5L772,525L766.2,524.5L766.7,516ZM775.5,516L781.2,516.5L780.7,525L774.9,524.5L775.5,516ZM784.2,516L790,516.5L789.5,525L783.7,524.5L784.2,516Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,501L745.3,501.5L744.8,510L739,509.5L739.5,501ZM748.3,501L754,501.5L753.5,510L747.7,509.5L748.3,501ZM757,501L762.8,501.5L762.2,510L756.5,509.5L757,501ZM765.7,501L771.5,501.5L771,510L765.2,509.5L765.7,501ZM774.5,501L780.2,501.5L779.7,510L773.9,509.5L774.5,501ZM783.2,501L789,501.5L788.5,510L782.7,509.5L783.2,501Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,488L745.3,488.5L744.8,497L739,496.5L739.5,488ZM748.3,488L754,488.5L753.5,497L747.7,496.5L748.3,488ZM757,488L762.8,488.5L762.2,497L756.5,496.5L757,488ZM765.7,488L771.5,488.5L771,497L765.2,496.5L765.7,488ZM774.5,488L780.2,488.5L779.7,497L773.9,496.5L774.5,488ZM783.2,488L789,488.5L788.5,497L782.7,496.5L783.2,488Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,475L745.3,475.5L744.8,484L739,483.5L739.5,475ZM748.3,475L754,475.5L753.5,484L747.7,483.5L748.3,475ZM757,475L762.8,475.5L762.2,484L756.5,483.5L757,475ZM765.7,475L771.5,475.5L771,484L765.2,483.5L765.7,475ZM774.5,475L780.2,475.5L779.7,484L773.9,483.5L774.5,475ZM783.2,475L789,475.5L788.5,484L782.7,483.5L783.2,475Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,462L745.3,462.5L744.8,471L739,470.5L739.5,462ZM748.3,462L754,462.5L753.5,471L747.7,470.5L748.3,462ZM757,462L762.8,462.5L762.2,471L756.5,470.5L757,462ZM765.7,462L771.5,462.5L771,471L765.2,470.5L765.7,462ZM774.5,462L780.2,462.5L779.7,471L773.9,470.5L774.5,462ZM783.2,462L789,462.5L788.5,471L782.7,470.5L783.2,462Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,447L745.3,447.5L744.8,456L739,455.5L739.5,447ZM748.3,447L754,447.5L753.5,456L747.7,455.5L748.3,447ZM757,447L762.8,447.5L762.2,456L756.5,455.5L757,447ZM765.7,447L771.5,447.5L771,456L765.2,455.5L765.7,447ZM774.5,447L780.2,447.5L779.7,456L773.9,455.5L774.5,447ZM783.2,447L789,447.5L788.5,456L782.7,455.5L783.2,447Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,434L745.3,434.5L744.8,443L739,442.5L739.5,434ZM748.3,434L754,434.5L753.5,443L747.7,442.5L748.3,434ZM757,434L762.8,434.5L762.2,443L756.5,442.5L757,434ZM765.7,434L771.5,434.5L771,443L765.2,442.5L765.7,434ZM774.5,434L780.2,434.5L779.7,443L773.9,442.5L774.5,434ZM783.2,434L789,434.5L788.5,443L782.7,442.5L783.2,434Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739.5,419L745.2,419.5L744.7,428L739,427.5L739.5,419ZM748.1,419L753.7,419.5L753.2,428L747.6,427.5L748.1,419ZM756.6,419L762.3,419.5L761.8,428L756.1,427.5L756.6,419ZM765.2,419L770.9,419.5L770.3,428L764.7,427.5L765.2,419ZM773.8,419L779.4,419.5L778.9,428L773.2,427.5L773.8,419ZM782.3,419L788,419.5L787.5,428L781.8,427.5L782.3,419Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M740.5,406L745.7,406.5L745.2,415L740,414.5L740.5,406ZM748.3,406L753.5,406.5L753.1,415L747.9,414.5L748.3,406ZM756.2,406L761.4,406.5L760.9,415L755.7,414.5L756.2,406ZM764.1,406L769.3,406.5L768.8,415L763.6,414.5L764.1,406ZM771.9,406L777.1,406.5L776.7,415L771.5,414.5L771.9,406ZM779.8,406L785,406.5L784.5,415L779.3,414.5L779.8,406Z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M669,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M679.6,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M690.1,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M700.7,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M711.3,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M721.9,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M732.4,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M743,275h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M668,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M678.6,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M689.1,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M699.7,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M710.3,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M720.9,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M731.4,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M742,289.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M665,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M675.6,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M686.1,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M696.7,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M707.3,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M717.9,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M728.4,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M739,303.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M664,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M674.6,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M685.1,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M695.7,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M706.3,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M716.9,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M727.4,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M738,317.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M661,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.6,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M682.1,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.7,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M703.3,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.9,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724.4,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M735,331.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M659,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M669.6,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M680.1,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M690.7,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M701.3,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M711.9,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M722.4,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M733,345.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M658,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M668.6,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M679.1,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M689.7,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M700.3,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M710.9,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M721.4,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M732,359.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M655,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M665.6,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M676.1,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M686.7,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M697.3,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M707.9,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M718.4,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M729,373.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M654,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M664.6,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M675.1,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M685.7,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M696.3,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M706.9,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M717.4,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M728,387.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M651,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M661.6,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M672.1,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M682.7,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M693.3,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M703.9,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M714.4,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M725,401.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M651,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M661.6,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M672.1,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M682.7,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M693.3,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M703.9,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M714.4,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M725,415.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,430h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,444.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,458.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,472.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,486.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,500.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,514.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,528.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,542.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,556.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,570.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,585h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,599.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,613.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,627.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,641.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,655.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,669.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,683.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,697.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,711.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,725.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,740h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,754.1h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,768.2h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,782.3h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,796.4h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,810.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,824.5h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,838.6h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,852.7h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,866.8h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,880.9h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M650,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M660.6,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M671.1,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M681.7,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M692.3,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M702.9,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M713.4,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M724,895h7v9h-7z"
      android:strokeWidth="1"
      android:fillColor="#72A197"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M78,930h898v31h-898z"
      android:strokeWidth="1"
      android:fillColor="#63577E"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
