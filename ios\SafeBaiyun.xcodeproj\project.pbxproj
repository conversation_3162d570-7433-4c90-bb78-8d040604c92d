// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789001A /* SafeBaiyunApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789001B /* SafeBaiyunApp.swift */; };
		A1234567890123456789001C /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789001D /* ContentView.swift */; };
		A1234567890123456789001E /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789001F /* Assets.xcassets */; };
		A1234567890123456789002A /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789002B /* Preview Assets.xcassets */; };
		A1234567890123456789010A /* SharedModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789010B /* SharedModels.swift */; };
		A1234567890123456789011A /* BLEManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789011B /* BLEManager.swift */; };
		A1234567890123456789011C /* BLEDevice.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789011D /* BLEDevice.swift */; };
		A1234567890123456789012A /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012B /* SettingsView.swift */; };
		A1234567890123456789012C /* DeviceSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012D /* DeviceSelectionView.swift */; };
		A1234567890123456789013A /* LockCrypto.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013B /* LockCrypto.swift */; };
		A1234567890123456789013C /* DESCrypto.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789013D /* DESCrypto.swift */; };
		/* Widget Extension Build Files */
		A1234567890123456789020A /* SafeBaiyunLockScreenWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789020B /* SafeBaiyunLockScreenWidget.swift */; };
		A1234567890123456789021A /* LockScreenWidgetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789021B /* LockScreenWidgetView.swift */; };
		A1234567890123456789022A /* SharedModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789010B /* SharedModels.swift */; };
		A1234567890123456789023A /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1234567890123456789023B /* WidgetKit.framework */; };
		A1234567890123456789024A /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1234567890123456789024B /* SwiftUI.framework */; };
		A1234567890123456789025A /* SafeBaiyunLockScreenWidgetExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = A1234567890123456789025B /* SafeBaiyunLockScreenWidgetExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A1234567890123456789035A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = A1234567890123456789004F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = A1234567890123456789031A;
			remoteInfo = SafeBaiyunLockScreenWidgetExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A1234567890123456789026A /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				A1234567890123456789025A /* SafeBaiyunLockScreenWidgetExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		A1234567890123456789001B /* SafeBaiyunApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunApp.swift; sourceTree = "<group>"; };
		A1234567890123456789001D /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890123456789001F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789002B /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890123456789005C /* 平安回家.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "平安回家.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789005D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789005E /* SafeBaiyun.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SafeBaiyun.entitlements; sourceTree = "<group>"; };
		A1234567890123456789010B /* SharedModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedModels.swift; sourceTree = "<group>"; };
		A1234567890123456789011B /* BLEManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BLEManager.swift; sourceTree = "<group>"; };
		A1234567890123456789011D /* BLEDevice.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BLEDevice.swift; sourceTree = "<group>"; };
		A1234567890123456789012B /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		A1234567890123456789012D /* DeviceSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceSelectionView.swift; sourceTree = "<group>"; };
		A1234567890123456789013B /* LockCrypto.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LockCrypto.swift; sourceTree = "<group>"; };
		A1234567890123456789013D /* DESCrypto.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DESCrypto.swift; sourceTree = "<group>"; };
		/* Widget Extension File References */
		A1234567890123456789020B /* SafeBaiyunLockScreenWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunLockScreenWidget.swift; sourceTree = "<group>"; };
		A1234567890123456789021B /* LockScreenWidgetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LockScreenWidgetView.swift; sourceTree = "<group>"; };
		A1234567890123456789022B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789023B /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		A1234567890123456789024B /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		A1234567890123456789025B /* SafeBaiyunLockScreenWidgetExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = SafeBaiyunLockScreenWidgetExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789027B /* SafeBaiyunLockScreenWidget.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SafeBaiyunLockScreenWidget.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890123456789006B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1234567890123456789028A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789024A /* SwiftUI.framework in Frameworks */,
				A1234567890123456789023A /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890123456789006D = {
			isa = PBXGroup;
			children = (
				A1234567890123456789006E /* SafeBaiyun */,
				A1234567890123456789029A /* SafeBaiyunLockScreenWidget */,
				A1234567890123456789008A /* Shared */,
				A1234567890123456789007A /* Frameworks */,
				A1234567890123456789007B /* Products */,
			);
			sourceTree = "<group>";
		};
		A1234567890123456789006E /* SafeBaiyun */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789001B /* SafeBaiyunApp.swift */,
				A1234567890123456789007C /* Views */,
				A1234567890123456789007D /* BLE */,
				A1234567890123456789007E /* Crypto */,
				A1234567890123456789007F /* Storage */,
				A1234567890123456789001F /* Assets.xcassets */,
				A1234567890123456789005D /* Info.plist */,
				A1234567890123456789005E /* SafeBaiyun.entitlements */,
				A1234567890123456789008B /* Preview Content */,
			);
			path = SafeBaiyun;
			sourceTree = "<group>";
		};
		A1234567890123456789007A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789023B /* WidgetKit.framework */,
				A1234567890123456789024B /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A1234567890123456789007B /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789005C /* 平安回家.app */,
				A1234567890123456789025B /* SafeBaiyunLockScreenWidgetExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890123456789007C /* Views */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789001D /* ContentView.swift */,
				A1234567890123456789012B /* SettingsView.swift */,
				A1234567890123456789012D /* DeviceSelectionView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1234567890123456789007D /* BLE */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789011B /* BLEManager.swift */,
				A1234567890123456789011D /* BLEDevice.swift */,
			);
			path = BLE;
			sourceTree = "<group>";
		};
		A1234567890123456789007E /* Crypto */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789013B /* LockCrypto.swift */,
				A1234567890123456789013D /* DESCrypto.swift */,
			);
			path = Crypto;
			sourceTree = "<group>";
		};
		A1234567890123456789007F /* Storage */ = {
			isa = PBXGroup;
			children = (
			);
			path = Storage;
			sourceTree = "<group>";
		};
		A1234567890123456789008A /* Shared */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789010B /* SharedModels.swift */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		A1234567890123456789008B /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789002B /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		A1234567890123456789029A /* SafeBaiyunLockScreenWidget */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789020B /* SafeBaiyunLockScreenWidget.swift */,
				A1234567890123456789021B /* LockScreenWidgetView.swift */,
				A1234567890123456789022B /* Info.plist */,
				A1234567890123456789027B /* SafeBaiyunLockScreenWidget.entitlements */,
			);
			path = SafeBaiyunLockScreenWidget;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456789008C /* SafeBaiyun */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456789008D /* Build configuration list for PBXNativeTarget "SafeBaiyun" */;
			buildPhases = (
				A1234567890123456789008E /* Sources */,
				A1234567890123456789006B /* Frameworks */,
				A1234567890123456789008F /* Resources */,
				A1234567890123456789026A /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				A1234567890123456789030A /* PBXTargetDependency */,
			);
			name = SafeBaiyun;
			productName = SafeBaiyun;
			productReference = A1234567890123456789005C /* 平安回家.app */;
			productType = "com.apple.product-type.application";
		};
		A1234567890123456789031A /* SafeBaiyunLockScreenWidgetExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456789032A /* Build configuration list for PBXNativeTarget "SafeBaiyunLockScreenWidgetExtension" */;
			buildPhases = (
				A1234567890123456789033A /* Sources */,
				A1234567890123456789028A /* Frameworks */,
				A1234567890123456789034A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SafeBaiyunLockScreenWidgetExtension;
			productName = SafeBaiyunLockScreenWidgetExtension;
			productReference = A1234567890123456789025B /* SafeBaiyunLockScreenWidgetExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456789004F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890123456789008C = {
						CreatedOnToolsVersion = 15.0;
					};
					A1234567890123456789031A = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1234567890123456789009E /* Build configuration list for PBXProject "SafeBaiyun" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				"zh-Hans",
			);
			mainGroup = A1234567890123456789006D;
			productRefGroup = A1234567890123456789007B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456789008C /* SafeBaiyun */,
				A1234567890123456789031A /* SafeBaiyunLockScreenWidgetExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1234567890123456789008F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789002A /* Preview Assets.xcassets in Resources */,
				A1234567890123456789001E /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1234567890123456789034A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1234567890123456789008E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789001C /* ContentView.swift in Sources */,
				A1234567890123456789001A /* SafeBaiyunApp.swift in Sources */,
				A1234567890123456789010A /* SharedModels.swift in Sources */,
				A1234567890123456789011A /* BLEManager.swift in Sources */,
				A1234567890123456789011C /* BLEDevice.swift in Sources */,
				A1234567890123456789012A /* SettingsView.swift in Sources */,
				A1234567890123456789012C /* DeviceSelectionView.swift in Sources */,
				A1234567890123456789013A /* LockCrypto.swift in Sources */,
				A1234567890123456789013C /* DESCrypto.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A1234567890123456789033A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789020A /* SafeBaiyunLockScreenWidget.swift in Sources */,
				A1234567890123456789021A /* LockScreenWidgetView.swift in Sources */,
				A1234567890123456789022A /* SharedModels.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A1234567890123456789030A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = A1234567890123456789031A /* SafeBaiyunLockScreenWidgetExtension */;
			targetProxy = A1234567890123456789035A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		A12345678901234567890010 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A12345678901234567890011 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SafeBaiyun/SafeBaiyun.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = YDW3FB8774;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "平安回家";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "此应用需要使用蓝牙连接门禁设备进行开门操作";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "此应用需要使用蓝牙连接门禁设备进行开门操作";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "bluetooth-le";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.safebaiyun;
				PRODUCT_NAME = "平安回家";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A12345678901234567890012 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = SafeBaiyun/SafeBaiyun.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = YDW3FB8774;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "平安回家";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "此应用需要使用蓝牙连接门禁设备进行开门操作";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "此应用需要使用蓝牙连接门禁设备进行开门操作";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "bluetooth-le";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "$(DEVELOPMENT_TEAM).safebaiyun";
				PRODUCT_NAME = "平安回家";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A1234567890123456789036A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = SafeBaiyunLockScreenWidget/SafeBaiyunLockScreenWidget.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YDW3FB8774;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SafeBaiyunLockScreenWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "平安回家锁屏小组件";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "$(DEVELOPMENT_TEAM).safebaiyun.SafeBaiyunLockScreenWidget";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1234567890123456789037A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = SafeBaiyunLockScreenWidget/SafeBaiyunLockScreenWidget.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YDW3FB8774;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = SafeBaiyunLockScreenWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "平安回家锁屏小组件";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "$(DEVELOPMENT_TEAM).safebaiyun.SafeBaiyunLockScreenWidget";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		A1234567890123456789009F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1234567890123456789008D /* Build configuration list for PBXNativeTarget "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A12345678901234567890011 /* Debug */,
				A12345678901234567890012 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890123456789009E /* Build configuration list for PBXProject "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789009F /* Debug */,
				A12345678901234567890010 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890123456789032A /* Build configuration list for PBXNativeTarget "SafeBaiyunLockScreenWidgetExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789036A /* Debug */,
				A1234567890123456789037A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1234567890123456789004F /* Project object */;
}
