<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />

    <application
        android:name="cn.huacheng.safebaiyun.BaiYunApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SafeBaiyun"
        tools:targetApi="31">

        <activity
            android:name="cn.huacheng.safebaiyun.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.SafeBaiyun">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>


        <activity
            android:name=".ShortcutActivity"
            android:exported="true" />

        <activity-alias
            android:name="cn.huacheng.safebaiyun.QuickUnlockActivity"
            android:exported="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/unlock_door"
            android:targetActivity=".ShortcutActivity">
            <intent-filter>
                <action android:name="android.intent.action.CREATE_SHORTCUT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <receiver
            android:name=".widget.MediumReceiver"
            android:exported="true">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/medium_widget_config" />
        </receiver>

        <receiver
            android:name=".widget.LargeReceiver"
            android:exported="true">

            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/large_widget_config" />
        </receiver>

    </application>

</manifest>
