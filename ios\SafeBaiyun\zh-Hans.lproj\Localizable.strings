/* 
  Localizable.strings
  SafeBaiyun

  Created by SafeBaiyun on 2024/12/27.
  
*/

// MARK: - App Name
"app_name" = "平安回家";
"app_subtitle" = "蓝牙门禁开门助手";

// MARK: - Connection States
"connection_state_disconnected" = "未连接";
"connection_state_scanning" = "搜索设备中";
"connection_state_connecting" = "连接中";
"connection_state_connected" = "已连接";
"connection_state_authenticating" = "认证中";
"connection_state_ready" = "就绪";
"connection_state_error" = "连接错误";

// MARK: - Main Interface
"unlock_button" = "开门";
"reconnect_button" = "重新连接";
"settings_button" = "设置";
"logs_button" = "日志";
"configure_device_button" = "配置设备";

// MARK: - Status
"connection_status" = "连接状态";
"bound_device" = "绑定设备";
"last_operation" = "上次操作";
"configured_device" = "已配置设备";

// MARK: - Settings
"device_settings" = "设备设置";
"device_configuration" = "设备配置";
"device_info" = "设备信息";
"mac_address" = "MAC地址";
"encryption_key" = "加密密钥";
"mac_address_placeholder" = "例如: AA:BB:CC:DD:EE:FF";
"encryption_key_placeholder" = "请输入加密密钥";
"save_configuration" = "保存配置";
"search_and_bind_device" = "搜索并绑定设备";
"clear_configuration" = "清除配置";
"current_configuration" = "当前配置";
"device_name" = "设备名称";
"configuration_time" = "配置时间";
"binding_status" = "绑定状态";
"bound" = "已绑定";
"not_bound" = "未绑定";
"not_configured" = "未配置";

// MARK: - Device Selection
"select_device" = "选择设备";
"discovered_devices" = "发现的设备";
"no_devices_found" = "未发现设备";
"searching_devices" = "正在搜索设备...";
"ensure_device_nearby" = "请确保门禁设备已开启并在附近";
"rescan" = "重新扫描";
"scan" = "扫描";
"stop" = "停止";
"cancel" = "取消";
"connecting_device" = "连接设备";
"contains_target_service" = "包含目标服务";
"manufacturer_data" = "制造商数据";
"discovery_time" = "发现时间";
"unknown_device" = "未知设备";

// MARK: - Signal Strength
"signal_excellent" = "信号极强";
"signal_very_good" = "信号很强";
"signal_good" = "信号良好";
"signal_fair" = "信号较弱";
"signal_poor" = "信号很弱";

// MARK: - Operations
"unlock_success" = "开门成功";
"unlock_failed" = "开门失败";
"attempting_unlock" = "正在尝试开门...";
"device_binding_success" = "设备绑定成功";
"device_connection_success" = "设备连接成功！已自动绑定此设备。";
"connection_failed" = "连接失败";

// MARK: - Errors
"bluetooth_not_enabled" = "请开启蓝牙";
"bluetooth_unauthorized" = "请授权蓝牙权限";
"bluetooth_unsupported" = "设备不支持蓝牙";
"device_not_configured" = "请先配置设备信息";
"device_not_bound" = "请先绑定设备";
"device_not_ready" = "设备未连接";
"device_not_in_range" = "设备不在范围内";
"invalid_mac_address" = "MAC地址格式错误";
"invalid_encryption_key" = "加密密钥无效";
"connection_timeout" = "连接超时";
"service_discovery_failed" = "服务发现失败";
"characteristic_discovery_failed" = "特征发现失败";
"read_data_failed" = "读取数据失败";
"write_data_failed" = "写入数据失败";
"encryption_failed" = "数据加密失败";

// MARK: - Help
"help_title" = "如何获取MAC地址和密钥？";
"help_root_method" = "1. 有Root权限的Android设备：";
"help_root_description" = "前往 /data/data/com.huacheng.baiyunuser/databases/ 目录，找到数据库文件";
"help_no_root_method" = "2. 无Root权限的设备：";
"help_no_root_description" = "使用手机备份功能提取应用数据，然后查看数据库文件";
"help_database_method" = "3. 在数据库的t_device表中：";
"help_database_description" = "MAC_NUM 字段是MAC地址，PRODUCT_KEY 字段是加密密钥";

// MARK: - Alerts
"alert_title" = "提示";
"ok" = "确定";
"done" = "完成";
"close" = "关闭";
"configuration_saved" = "配置保存成功！现在可以搜索并绑定设备。";
"configuration_cleared" = "配置已清除";

// MARK: - Widget
"widget_display_name" = "平安回家";
"widget_description" = "快速开门和查看设备状态";
"opening_app" = "正在打开应用执行开门操作...";
"reconnecting_device" = "正在重新连接设备...";
"opening_settings" = "正在打开设置...";

// MARK: - Logs
"operation_logs" = "操作日志";
"ble_manager_initialized" = "BLE管理器初始化完成";
"bluetooth_state_unknown" = "蓝牙状态未知";
"bluetooth_resetting" = "蓝牙正在重置";
"bluetooth_powered_on" = "蓝牙已开启";
"bluetooth_powered_off" = "蓝牙已关闭";
"start_scanning" = "开始扫描设备...";
"stop_scanning" = "停止扫描";
"device_discovered" = "发现设备";
"connection_successful" = "设备连接成功";
"connection_disconnected" = "设备已断开连接";
"services_discovered" = "发现服务";
"characteristics_discovered" = "发现特征";
"readable_characteristic_found" = "找到可读特征";
"writable_characteristic_found" = "找到可写特征";
"notification_characteristic_found" = "找到通知特征";
"device_ready" = "设备特征准备完成，可以执行操作";
"reading_random_data" = "开始读取设备随机数...";
"data_read" = "读取到数据";
"generating_encrypted_data" = "开始生成加密数据...";
"writing_encrypted_data" = "写入加密数据";
"key_write_successful" = "密钥写入成功，开门完成";
"key_write_failed" = "密钥写入失败";
