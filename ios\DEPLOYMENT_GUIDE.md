# 平安回家 iOS 版本部署指南

## 项目概述

本项目是广州市白云区蓝牙门禁应用的iOS移植版本，完整实现了Android版本的所有核心功能，并针对iOS平台进行了特殊适配。

## 系统要求

- **开发环境**: Xcode 15.0+
- **iOS版本**: 17.0+
- **Swift版本**: 5.0+
- **设备要求**: 支持蓝牙4.0+的iPhone/iPad

## 项目结构

```
ios/
├── SafeBaiyun.xcodeproj/          # Xcode项目文件
├── SafeBaiyun/                    # 主应用
│   ├── SafeBaiyunApp.swift       # 应用入口
│   ├── Views/                    # SwiftUI界面
│   ├── BLE/                      # 蓝牙管理
│   ├── Crypto/                   # 加密算法
│   ├── Storage/                  # 数据存储
│   ├── Shared/                   # 共享模块
│   ├── Intents/                  # AppIntents
│   ├── Assets.xcassets/          # 资源文件
│   ├── Preview Content/          # 预览内容
│   ├── zh-Hans.lproj/           # 中文本地化
│   ├── en.lproj/                # 英文本地化
│   ├── Info.plist               # 应用配置
│   └── SafeBaiyun.entitlements  # 权限配置
├── SafeBaiyunWidgetExtension/     # Widget扩展
│   ├── SafeBaiyunWidget.swift    # Widget实现
│   ├── Assets.xcassets/          # Widget资源
│   └── Info.plist               # Widget配置
├── Shared/                       # 共享文件
│   ├── SharedModels.swift        # 数据模型
│   └── UnlockIntent.swift        # AppIntents定义
└── README.md                     # 使用说明
```

## 部署步骤

### 1. 环境准备

1. 确保安装了Xcode 15.0或更高版本
2. 确保有有效的Apple Developer账号
3. 在Xcode中登录Apple Developer账号

### 2. 项目配置

1. 打开 `SafeBaiyun.xcodeproj`
2. 选择项目根节点，在 "Signing & Capabilities" 中：
   - 设置正确的Team
   - 修改Bundle Identifier为你的唯一标识符
   - 确保App Groups配置正确

3. 对Widget扩展重复上述步骤

### 3. 权限配置

项目已预配置以下权限：
- **蓝牙权限**: `NSBluetoothAlwaysUsageDescription`
- **后台蓝牙**: `bluetooth-central` background mode
- **App Groups**: 用于主应用和Widget数据共享

### 4. 编译和运行

1. 选择目标设备或模拟器
2. 点击 "Build and Run" (⌘+R)
3. 首次运行时会请求蓝牙权限，请允许

### 5. Widget配置

1. 应用安装后，长按桌面空白处
2. 点击左上角的 "+" 号
3. 搜索 "平安回家"
4. 选择小组件尺寸并添加到桌面

## 功能验证

### 主要功能测试

1. **设备配置**:
   - 打开应用，点击"设置"
   - 输入MAC地址和加密密钥
   - 保存配置

2. **设备绑定**:
   - 点击"搜索并绑定设备"
   - 从列表中选择正确的门禁设备
   - 确认连接成功

3. **开门操作**:
   - 在主界面点击"开门"按钮
   - 或在Widget中点击开门按钮
   - 验证门禁设备响应

4. **Widget功能**:
   - 验证Widget显示正确的设备状态
   - 测试Widget按钮功能
   - 确认Widget操作能正确打开主应用

### 错误处理测试

1. **蓝牙未开启**: 验证提示用户开启蓝牙
2. **设备未配置**: 验证提示配置设备信息
3. **连接失败**: 验证重连机制和错误提示
4. **加密失败**: 验证密钥错误时的处理

## 常见问题

### 编译错误

1. **"Cannot find type 'WidgetData' in scope"**:
   - 确保Shared文件夹中的文件被正确添加到项目
   - 检查项目配置中的文件引用

2. **签名错误**:
   - 检查Bundle Identifier是否唯一
   - 确保Apple Developer账号有效
   - 检查Provisioning Profile

### 运行时问题

1. **蓝牙权限被拒绝**:
   - 在设置中手动开启应用的蓝牙权限
   - 重启应用

2. **Widget不显示数据**:
   - 检查App Groups配置是否正确
   - 确保主应用和Widget使用相同的App Groups ID

3. **设备连接失败**:
   - 确保门禁设备在附近且正常工作
   - 检查MAC地址和密钥是否正确
   - 尝试重新绑定设备

## 发布准备

### App Store发布

1. **版本信息**:
   - 更新版本号和构建号
   - 准备应用截图和描述

2. **隐私信息**:
   - 声明蓝牙使用目的
   - 说明数据收集和使用情况

3. **测试**:
   - 在多个设备上测试
   - 验证所有功能正常工作
   - 测试Widget在不同iOS版本的兼容性

### 企业分发

1. **证书配置**:
   - 使用企业证书签名
   - 配置企业分发Provisioning Profile

2. **分发方式**:
   - 通过企业应用商店分发
   - 或通过OTA方式分发

## 维护和更新

### 日志收集

应用内置了详细的操作日志功能：
- 在主界面点击"日志"查看详细记录
- 日志包含BLE连接、加密、操作等信息
- 可用于问题诊断和性能优化

### 数据备份

用户数据存储在App Groups中：
- 设备配置信息
- 连接状态
- 操作历史

### 版本更新

更新时需要注意：
- 保持数据模型兼容性
- 测试Widget功能
- 验证加密算法兼容性

## 技术支持

如遇到技术问题，请提供以下信息：
- iOS版本和设备型号
- 应用版本号
- 详细的错误描述
- 操作日志（如果可能）

## 许可证

本项目基于原Android版本进行移植，请遵守相关许可证条款。
