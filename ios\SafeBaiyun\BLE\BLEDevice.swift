//
//  BLEDevice.swift
//  SafeBaiyun
//
//  Created by SafeBaiyun on 2024/12/27.
//

import Foundation
import CoreBluetooth

/// BLE设备扩展，添加便利方法
extension BLEDevice {
    /// 获取设备显示名称
    var displayName: String {
        if let name = name, !name.isEmpty {
            return name
        }
        return "未知设备"
    }
    
    /// 获取信号强度描述
    var signalStrengthDescription: String {
        switch rssi {
        case -30...0:
            return "信号极强"
        case -50..<(-30):
            return "信号很强"
        case -70..<(-50):
            return "信号良好"
        case -85..<(-70):
            return "信号较弱"
        default:
            return "信号很弱"
        }
    }
    
    /// 获取信号强度等级（1-5）
    var signalStrengthLevel: Int {
        switch rssi {
        case -30...0:
            return 5
        case -50..<(-30):
            return 4
        case -70..<(-50):
            return 3
        case -85..<(-70):
            return 2
        default:
            return 1
        }
    }
    
    /// 获取制造商数据的十六进制字符串
    var manufacturerDataHex: String? {
        guard let advertisementData = advertisementData,
              let manufacturerData = advertisementData[CBAdvertisementDataManufacturerDataKey] as? Data else {
            return nil
        }
        return manufacturerData.hexString
    }
    
    /// 获取服务UUID列表
    var serviceUUIDs: [String] {
        guard let advertisementData = advertisementData,
              let serviceUUIDs = advertisementData[CBAdvertisementDataServiceUUIDsKey] as? [CBUUID] else {
            return []
        }
        return serviceUUIDs.map { $0.uuidString }
    }
    
    /// 检查是否包含目标服务
    var hasTargetService: Bool {
        return serviceUUIDs.contains { $0.lowercased() == Constants.magicServiceUUID.lowercased() }
    }
    
    /// 获取发现时间的格式化字符串
    var discoveredTimeString: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .medium
        return formatter.string(from: discoveredAt)
    }
    
    /// 获取设备信息摘要
    var deviceSummary: String {
        var summary = displayName
        if rssi != 0 {
            summary += " (RSSI: \(rssi)dBm)"
        }
        if let manufacturerData = manufacturerDataHex {
            summary += " [制造商数据: \(manufacturerData.prefix(16))...]"
        }
        return summary
    }
}

/// BLE设备比较和排序
extension BLEDevice: Comparable {
    static func < (lhs: BLEDevice, rhs: BLEDevice) -> Bool {
        // 首先按是否包含目标服务排序
        if lhs.hasTargetService != rhs.hasTargetService {
            return lhs.hasTargetService && !rhs.hasTargetService
        }
        
        // 然后按信号强度排序（RSSI越大越好）
        if lhs.rssi != rhs.rssi {
            return lhs.rssi > rhs.rssi
        }
        
        // 最后按发现时间排序（越新越好）
        return lhs.discoveredAt > rhs.discoveredAt
    }
}
